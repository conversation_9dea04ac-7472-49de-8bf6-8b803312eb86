<?xml version="1.0" encoding="UTF-8"?>
<web-app>

    <display-name>scgl</display-name>

    <servlet>
        <servlet-name>ScglApiServlet</servlet-name>
        <!--容器-->
        <servlet-class>org.glassfish.jersey.servlet.ServletContainer</servlet-class>
        <init-param>
            <param-name>jersey.config.server.provider.packages</param-name>
            <!--映射的包名，用于搜索处理类-->
            <param-value>shanks.scgl.service</param-value>
        </init-param>
        <init-param>
            <param-name>javax.ws.rs.Application</param-name>
            <param-value>shanks.scgl.Application</param-value>
        </init-param>

        <load-on-startup>1</load-on-startup>
    </servlet>

    <!--映射-->
    <servlet-mapping>
        <servlet-name>ScglApiServlet</servlet-name>
        <!--访问路径-->
        <url-pattern>/*</url-pattern>
    </servlet-mapping>
</web-app>
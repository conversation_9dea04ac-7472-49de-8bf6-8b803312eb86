package shanks.scgl.factory;

import shanks.scgl.bean.api.message.MessageCreateModel;
import shanks.scgl.bean.db.Message;
import shanks.scgl.bean.db.User;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 消息数据处理
 */
public class MessageFactory {
    public static Message findById(String id) {
        return Hib.query(session -> session.get(Message.class, id));
    }

    //添加一条普通消息
    public static Message add(User sender, User receiver, MessageCreateModel model) {
        Message message = new Message(sender, receiver, model);
        return save(message);
    }

    private static Message save(Message message) {
        return Hib.query(session -> {
            session.save(message);

            //写入到数据库
            session.flush();

            //紧接着从数据库中查询出来
            session.refresh(message);

            return message;
        });
    }

    public static void delete(Message message) {
        Hib.queryOnly(session -> session.delete(message));
    }

    @SuppressWarnings("unchecked")
    public static List<Message> list(User user, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Message>) session
                .createQuery("from Message where receiverId =:uid or senderId=:uid and createAt>:min and " +
                        "createAt<:max order by createAt desc ")
                .setParameter("uid", user.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Message> list(User self, User other, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Message>) session
                .createQuery("from Message where (receiverId =:uid and senderId=:oid) or (receiverId =:oid and " +
                        "senderId=:uid) and createAt>:min and createAt<:max order by createAt desc ")
                .setParameter("uid", self.getId())
                .setParameter("oid", other.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }
}

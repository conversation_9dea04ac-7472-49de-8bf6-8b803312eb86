package shanks.scgl.factory;

import shanks.scgl.bean.api.anthology.AnthCreateModel;
import shanks.scgl.bean.api.message.MessageCreateModel;
import shanks.scgl.bean.db.*;
import shanks.scgl.utils.Hib;

import java.util.List;
import java.util.UUID;

public class AdminFactory {
    public static Anthology createOriginAnth(User user, int tempId) {
        AnthCreateModel model = new AnthCreateModel();
        model.setName("文集" + tempId);
        model.setIntro("这里是您的作品");
        model.setType(Anthology.TYPE_SELF);
        Anthology anthology = new Anthology(user, model);
        return AnthFactory.saveOrUpdate(anthology);
    }

    public static Anthology createKeepsAnth(User user, int tempId) {
        AnthCreateModel model = new AnthCreateModel();
        model.setName("收藏集" + tempId);
        model.setIntro("这里是收藏的作品");
        model.setType(Anthology.TYPE_KEEP);
        Anthology anthology = new Anthology(user, model);
        return AnthFactory.saveOrUpdate(anthology);
    }


    @SuppressWarnings("unchecked")
    public static void sendSysMsg(User sender, String content) {
        List<User> users = Hib.query(session -> (List<User>) session
                .createQuery("from User")
                .list());

        Hib.queryOnly(session -> {
            for (User user : users) {
                MessageCreateModel model = new MessageCreateModel();
                model.setId(UUID.randomUUID().toString());
                model.setContent(content);
                model.setReceiverId(user.getId());
                Message message = new Message(sender, user, model);

                session.save(message);
            }
        });
    }

    public static void sendWelcome(User receiver) {
        User sender = UserFactory.findById(1);
        Hib.queryOnly(session -> {
            MessageCreateModel model = new MessageCreateModel();
            model.setId(UUID.randomUUID().toString());
            model.setContent("欢迎加入诗词格律!点击左上角的头像可以完善个人信息,右上角的加号可以进行创作.点击[诗友],可以进行关注.[诗词曲]中是最新分享的作品.");
            model.setReceiverId(receiver.getId());
            Message message = new Message(sender, receiver, model);
            session.save(message);
            //暂时没用,需要梳理一下推送服务
            //PushFactory.pushNewMessage(sender, message);
        });
    }
}

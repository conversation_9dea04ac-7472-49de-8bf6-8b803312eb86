package shanks.scgl.factory;

import shanks.scgl.bean.db.Order;
import shanks.scgl.utils.Hib;

public class OrderFactory {

    @SuppressWarnings("Duplicates")
    public static Order saveOrUpdate(Order order) {
        return Hib.query(session -> {
            session.saveOrUpdate(order);
            //写入到数据库
            session.flush();
            //紧接着从数据库中查询出来
            session.refresh(order);
            return order;
        });
    }

}

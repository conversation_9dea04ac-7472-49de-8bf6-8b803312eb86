package shanks.scgl.factory;

import shanks.scgl.bean.card.SummaryItem;
import shanks.scgl.bean.db.Poem;
import shanks.scgl.bean.db.User;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.List;

public class PoemFactory {
    public static Poem findById(String id) {
        return Hib.query(session -> session.get(Poem.class, id));
    }

    @SuppressWarnings("Duplicates")
    public static Poem saveOrUpdate(Poem poem) {
        return Hib.query(session -> {
            session.saveOrUpdate(poem);
            //写入到数据库
            session.flush();
            //紧接着从数据库中查询出来
            session.refresh(poem);
            return poem;
        });
    }

    public static Poem delete(Poem poem) {
        return Hib.query(session -> {
            session.delete(poem);
            return poem;
        });
    }

    public static long count(User user) {
        return Hib.query(session -> (Long) session
                .createQuery("select count(*) from Poem where uid=:uid")
                .setParameter("uid", user.getId())
                .uniqueResult());
    }

    public static void delete(User user, String folder) {
        Hib.queryOnly(session -> session
                .createQuery("delete from Poem where folder=:folder and uid=:uid")
                .setParameter("folder", folder)
                .setParameter("uid", user.getId())
                .executeUpdate());
    }

    @SuppressWarnings("unchecked")
    public static List<Poem> list(User user, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Poem>) session
                .createQuery("from Poem where uid =:uid and updateAt>:min and updateAt<:max order by updateAt")
                .setParameter("uid", user.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Poem> listByCreateAt(User user, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Poem>) session
                .createQuery("from Poem where uid =:uid and createAt>:min and createAt<:max order by createAt desc")
                .setParameter("uid", user.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<SummaryItem> summary(User user) {
        return Hib.query(session -> (List<SummaryItem>) session
                .createQuery("SELECT folder,count(id) FROM Poem where uid=:uid group by folder")
                .setParameter("uid", user.getId())
                .list());
    }
}

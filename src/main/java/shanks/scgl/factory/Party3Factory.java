package shanks.scgl.factory;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

public class Party3Factory {
    /**
     * 获取微信OpenID
     * @param code 微信授权码
     * @return 微信OpenID
     */
    public static String getWxOpenID(String code) {
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token";
        String appid = "wxf4dcdad19b00227b"; // Replace with actual appid
        String secret = "********************************"; // Replace with actual secret
        String grant_type = "authorization_code";

        String requestUrl = String.format("%s?appid=%s&secret=%s&code=%s&grant_type=%s",
                url, appid, secret, code, grant_type);

        try {
            URL wxUrl = new URL(requestUrl);
            HttpURLConnection conn = (HttpURLConnection) wxUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            
            BufferedReader in = new BufferedReader(
                new InputStreamReader(conn.getInputStream(), "UTF-8")
            );
            String inputLine;
            StringBuilder response = new StringBuilder();
            
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();
            conn.disconnect();

            // Parse JSON response
            JsonObject jsonResponse = new Gson().fromJson(response.toString(), JsonObject.class);
            
            if (jsonResponse.has("errcode")) {
                // Handle error case
                return null;
            }
            
            return jsonResponse.get("openid").getAsString();

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}

package shanks.scgl.factory;

import com.google.common.base.Strings;
import org.hibernate.Session;
import shanks.scgl.bean.api.anthology.RecoCreateModel;
import shanks.scgl.bean.db.*;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.List;

public class AnthFactory {
    public static Anthology findById(String id) {
        if (Strings.isNullOrEmpty(id)) {
            return null;
        }
        return Hib.query(session -> session.get(Anthology.class, id));
    }

    @SuppressWarnings("Duplicates")
    public static Anthology saveOrUpdate(Anthology anth) {
        return Hib.query(session -> {
            session.saveOrUpdate(anth);
            session.flush();
            session.refresh(anth);

            updateAnthCount(session, anth.getUid());

            return anth;
        });


    }

    private static void updateAnthCount(Session session, int uid) {
        long count = (long) session
                .createQuery("select count(*) from Anthology where uid=:userId and isDelete=0 and type=0")
                .setParameter("userId", uid)
                .uniqueResult();

        User user = UserFactory.findById(uid);
        UserInfo info = user.loadInfo();
        info.setAnthCount((int) count);
        session.saveOrUpdate(info);

    }

    @SuppressWarnings("unchecked")
    public static List<Anthology> list(User user, int type, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Anthology>) session
                .createQuery("from Anthology where uid =:uid and type=:type and isDelete=0  " +
                        "and createAt>:min and createAt<:max order by createAt")
                .setParameter("uid", user.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setParameter("type", type)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Weibo> postOfOrigin(Anthology anth, LocalDateTime min, LocalDateTime max) {
        //时间按升序排列,确保都能下载完
        return Hib.query(session -> (List<Weibo>) session
                .createQuery("from Weibo where anthId =:anthId and createAt>:min and createAt<:max order by id")
                .setParameter("anthId", anth.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Keep> keeps(Anthology anth, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Keep>) session
                .createQuery("select K from Keep K LEFT JOIN FETCH K.weibo W where K.anthId=:anthId and K" +
                        ".createAt>:min and K.createAt<:max order by K.id")
                .setParameter("anthId", anth.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    /**
     * 确保Weibo一定有值,否则构造WeiboCard的时候会出问题
     *
     * @param kid Keep Id
     * @return Keep Obj
     */
    public static Keep getKeep(int kid) {
        return Hib.query(session -> {
            Keep keep = session.get(Keep.class, kid);
            Weibo weibo = session.get(Weibo.class, keep.getWid());
            keep.setWeibo(weibo);
            return keep;
        });
    }

    @SuppressWarnings("unchecked")
    public static List<Recommend> recos(Anthology anth, LocalDateTime min, LocalDateTime max) {
        //时间按升序排列,确保都能下载完
        return Hib.query(session -> (List<Recommend>) session
                .createQuery("from Recommend where anthId =:anthId and createAt>:min and createAt<:max order by " +
                        "createAt desc ")
                .setParameter("anthId", anth.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Recommend> recoMe(User user, LocalDateTime min, LocalDateTime max) {
        //时间按升序排列,确保都能下载完
        return Hib.query(session -> (List<Recommend>) session
                .createQuery("from Recommend where auid =:auid and createAt>:min and createAt<:max order by " +
                        "createAt desc ")
                .setParameter("auid", user.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Recommend> meReco(User user, LocalDateTime min, LocalDateTime max) {
        //时间按升序排列,确保都能下载完
        return Hib.query(session -> (List<Recommend>) session
                .createQuery("from Recommend where uid =:uid and createAt>:min and createAt<:max order by " +
                        "createAt desc ")
                .setParameter("uid", user.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static Anthology delete(Anthology anth) {
        return Hib.query(session -> {
            //删除
            anth.setDelete(1);
            session.saveOrUpdate(anth);
            session.flush();
            session.refresh(anth);

            updateAnthCount(session, anth.getUid());

            return anth;
        });
    }

    public static long getAnthSize(Anthology anthology) {
        if (anthology.getType() == Anthology.TYPE_KEEP) {
            return Hib.query(session -> (long) session
                    .createQuery("select count(*) from Keep where anthId=:anthId")
                    .setParameter("anthId", anthology.getId())
                    .uniqueResult());
        } else {
            return Hib.query(session -> (long) session
                    .createQuery("select count(*) from Weibo where anthId=:anthId")
                    .setParameter("anthId", anthology.getId())
                    .uniqueResult());
        }
    }

    public static long getRecoSize(Anthology anthology) {
        return Hib.query(session -> (long) session
                .createQuery("select count(*) from Recommend where anthId=:anthId")
                .setParameter("anthId", anthology.getId())
                .uniqueResult());
    }

    /**
     * 将作品移动到新的文集
     *
     * @param weibo     作品
     * @param anthology 新的位置
     * @return 移动后的作品obj
     */
    @SuppressWarnings("Duplicates")
    public static Weibo move(Weibo weibo, Anthology anthology) {
        return Hib.query(session -> {
            weibo.setAnthology(anthology);
            weibo.setAnthId(anthology.getId());

            session.saveOrUpdate(weibo);

            return weibo;
        });
    }

    @SuppressWarnings("Duplicates")
    public static Keep move(Keep keep, Anthology anthology) {
        return Hib.query(session -> {
            keep.setAnthology(anthology);
            keep.setAnthId(anthology.getId());

            session.saveOrUpdate(keep);

            return keep;
        });
    }

    public static Recommend recommend(Anthology anth, User recoUser, RecoCreateModel model) {
        return Hib.query(session -> {
            User anthOwner = session.get(User.class, anth.getUid());
            anth.setUser(anthOwner);
            //需要操作UserInfo,再load一次
            session.load(recoUser, recoUser.getId());

            UserInfo anthOwnerInfo = anthOwner.getUserInfo();
            UserInfo recoUserInfo = recoUser.getUserInfo();

            Recommend recommend = new Recommend(model, recoUser, anthOwner, anth);
            session.save(recommend);

            //文集增加10点人气
            anth.setPopular(anth.getPopular() + 10);
            //诗集owner的人气+1
            anthOwnerInfo.setTalent(anthOwnerInfo.getTalent() + 1);
            //推荐人的贡献+1,积分-1
            recoUserInfo.setContribute(recoUserInfo.getContribute() + 1);
            recoUserInfo.setPoint(recoUserInfo.getPoint() - 1);

            session.saveOrUpdate(anth);
            session.saveOrUpdate(anthOwner);
            session.saveOrUpdate(recoUser);
            //写入到数据库
            session.flush();
            //紧接着从数据库中查询出来
            session.refresh(recommend);
            return recommend;
        });
    }

    public static long getOriginAnthCount(User user) {
        return Hib.query(session -> (long) session
                .createQuery("select count(*) from Anthology where uid=:userId and isDelete=0 and type=0")
                .setParameter("userId", user.getId())
                .uniqueResult());
    }

    public static long getKeepAnthCount(User user) {
        return Hib.query(session -> (long) session
                .createQuery("select count(*) from Anthology where uid=:userId and isDelete=0 and type=1")
                .setParameter("userId", user.getId())
                .uniqueResult());
    }
}

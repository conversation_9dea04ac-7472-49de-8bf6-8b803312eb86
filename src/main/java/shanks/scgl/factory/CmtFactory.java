package shanks.scgl.factory;

import shanks.scgl.bean.view.CmtView;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CmtFactory {
    public static List<CmtView> listOfTop(int wid, LocalDateTime maxTime) {
        //noinspection unchecked
        return Hib.query(session -> (List<CmtView>) session
                .createQuery("from CmtView where wid =:wid and createAt<:time and parent=0 order by id desc ")
                .setParameter("wid", wid)
                .setParameter("time", maxTime)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static List<CmtView> listOfChild(int wid, ArrayList<Integer> ids) {
        //noinspection unchecked
        return Hib.query(session -> (List<CmtView>) session
                .createQuery("from CmtView where wid =:wid and parent in (:ids) order by id asc ")
                .setParameter("wid", wid)
                .setParameter("ids", ids)
                .list());
    }
}

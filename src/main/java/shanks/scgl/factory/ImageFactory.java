package shanks.scgl.factory;

import shanks.scgl.bean.db.Image;
import shanks.scgl.utils.Hib;

public class ImageFactory {
    public static Image findById(String id) {
        //通过Id查询更方便
        return Hib.query(session -> session.get(Image.class, id));
    }

    public static Image findByUrl(String url) {
        return Hib.query(session -> (Image) session
                .createQuery("from Image where url=:url")
                .setParameter("url", url)
                .uniqueResult());
    }

}

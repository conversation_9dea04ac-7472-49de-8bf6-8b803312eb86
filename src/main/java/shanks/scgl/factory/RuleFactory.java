package shanks.scgl.factory;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.rule.MarkCreateModel;
import shanks.scgl.bean.db.Mark;
import shanks.scgl.bean.db.Rule;
import shanks.scgl.bean.db.User;
import shanks.scgl.bean.db.UserInfo;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.List;

public class RuleFactory {
    public static Rule findById(String id) {
        return Hib.query(session -> session.get(Rule.class, id));
    }

    @SuppressWarnings("Duplicates")
    public static Rule saveOrUpdate(Rule rule) {
        return Hib.query(session -> {
            session.saveOrUpdate(rule);
            //写入到数据库
            session.flush();
            //紧接着从数据库中查询出来
            session.refresh(rule);
            return rule;
        });
    }


    @SuppressWarnings("unchecked")
    public static List<Rule> list(LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Rule>) session
                .createQuery("from Rule where status=:status and updateAt>:min and updateAt<:max order by updateAt " +
                        "desc ")
                .setParameter("min", min)
                .setParameter("max", max)
                .setParameter("status", Rule.STATUS_ON_LINE)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Rule> list(User user, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Rule>) session
                .createQuery("from Rule where uid=:uid and updateAt>:min and updateAt<:max order by updateAt " +
                        "desc ")
                .setParameter("min", min)
                .setParameter("max", max)
                .setParameter("uid", user.getId())
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Rule> list(String type, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Rule>) session
                .createQuery("from Rule where type=:type and status=:status and updateAt>:min and updateAt<:max order" +
                        " by updateAt desc")
                .setParameter("min", min)
                .setParameter("max", max)
                .setParameter("type", type)
                .setParameter("status", Rule.STATUS_ON_LINE)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Mark> marks(String ruleId, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Mark>) session
                .createQuery("from Mark where rid=:ruleId and createAt>:min and createAt<:max order" +
                        " by createAt desc")
                .setParameter("min", min)
                .setParameter("max", max)
                .setParameter("ruleId", ruleId)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Mark> markMe(User user, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Mark>) session
                .createQuery("from Mark where ruid=:ruid and createAt>:min and createAt<:max order" +
                        " by createAt desc")
                .setParameter("min", min)
                .setParameter("max", max)
                .setParameter("ruid", user.getId())
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Mark> myMark(User user, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Mark>) session
                .createQuery("from Mark where uid=:uid and createAt>:min and createAt<:max order" +
                        " by createAt desc")
                .setParameter("min", min)
                .setParameter("max", max)
                .setParameter("uid", user.getId())
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Rule> search(String name) {
        if (Strings.isNullOrEmpty(name)) {
            name = "";
        }
        final String searchName = "%" + name + "%";

        return Hib.query(session -> {
            //查询条件:name忽略大小写进行模糊查询
            return (List<Rule>) session
                    .createQuery("from Rule where lower(name) like :name and status=1 order by mark desc ")
                    .setParameter("name", searchName)
                    .setMaxResults(20).list();
        });
    }

    public static Mark findMark(Rule rule, String version, User user) {
        return Hib.query(session -> (Mark) session
                .createQuery("from Mark where rid=:rid and version=:version and uid=:uid ")
                .setParameter("rid", rule.getId())
                .setParameter("version", version)
                .setParameter("uid", user.getId())
                .uniqueResult());
    }

    public static Mark mark(Rule rule, User user, MarkCreateModel model) {
        return Hib.query(session -> {
            Mark mark = new Mark(model, user, rule);
            session.save(mark);

            //重新加载一下
            session.load(user, user.getId());

            //打分的人增加贡献值+1
            UserInfo userInfo = user.getUserInfo();
            userInfo.setContribute(userInfo.getContribute() + 1);
            session.saveOrUpdate(user);

            //被打分的人,根据评分等级增加贡献,也有可能减分
            float delta = model.getMark() - 2;
            if (delta != 0) {
                UserInfo ownerInfo = session.load(UserInfo.class, rule.getUid());
                ownerInfo.setContribute((int) (ownerInfo.getContribute() + delta));
                session.saveOrUpdate(ownerInfo);
            }


            //写入到数据库
            session.flush();
            //紧接着从数据库中查询出来
            session.refresh(mark);
            return mark;
        });
    }

    public static void reCalMark(Rule rule) {
        Hib.queryOnly(session -> {
            session.load(rule, rule.getId());
            List<Mark> marks = rule.getMarks();
            if (marks.size() == 0) {
                return;
            }
            float total = 0;
            for (Mark mark : marks) {
                total += mark.getMark();
            }
            float mark = total / marks.size();
            rule.setMark(mark);
            session.saveOrUpdate(rule);
        });
    }

    public static void recorderDownload(Rule rule) {
        Hib.queryOnly(session -> {
            rule.setDownload(rule.getDownload() + 1);
            session.saveOrUpdate(rule);
        });
    }

    @SuppressWarnings("unchecked")
    public static List<String> names(String type) {
        return Hib.query(session -> (List<String>) session
                .createQuery("select distinct name from Rule where type=:type and status=:status")
                .setParameter("type", type)
                .setParameter("status", Rule.STATUS_ON_LINE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Rule> rules(String name) {
        return Hib.query(session -> (List<Rule>) session
                .createQuery("from Rule where status=1 and name=:name")
                .setParameter("name", name)
                .list());
    }
}

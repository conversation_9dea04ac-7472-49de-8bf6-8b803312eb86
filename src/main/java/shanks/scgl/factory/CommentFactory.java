package shanks.scgl.factory;

import shanks.scgl.bean.api.weibo.CommentCreateModel;
import shanks.scgl.bean.db.Atme;
import shanks.scgl.bean.db.Comment;
import shanks.scgl.bean.db.User;
import shanks.scgl.bean.db.Weibo;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.List;

public class CommentFactory {
    /**
     * 获取指定微博的评论
     *
     * @param wid 微博id
     * @param min 最小时间
     * @param max 最大时间
     * @return List<WeiboComment>
     */
    public static List<Comment> list(int wid, LocalDateTime min, LocalDateTime max) {
        //noinspection unchecked
        return Hib.query(session -> (List<Comment>) session
                .createQuery("from Comment where wid =:wid and createAt>:min and createAt<:max order by id asc ")
                .setParameter("wid", wid)
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static List<Comment> listToMe(User self, LocalDateTime min, LocalDateTime max) {
        //noinspection unchecked
        return Hib.query(session -> (List<Comment>) session
                .createQuery("from Comment where wuid =:uid and uid!=:uid and createAt>:min and createAt<:max order " +
                        "by id desc ")
                .setParameter("uid", self.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static List<Comment> listMeTo(User self, LocalDateTime min, LocalDateTime max) {
        //noinspection unchecked
        return Hib.query(session -> (List<Comment>) session
                .createQuery("from Comment where uid =:uid and createAt>:min and createAt<:max order by id desc ")
                .setParameter("uid", self.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static List<Comment> listAtMe(User self, LocalDateTime min, LocalDateTime max) {
        //noinspection unchecked
        return Hib.query(session -> (List<Comment>) session
                .createQuery("select A.comment from Atme A where A.uid=:uid and " +
                        "A.createAt>:min and A.createAt<:max order by A.id desc")
                .setParameter("uid", self.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static Comment findById(int id) {
        return Hib.query(session -> session.get(Comment.class, id));
    }

    public static Comment delete(Comment comment) {
        return Hib.query(session -> {
            comment.setDelete(1);
            session.saveOrUpdate(comment);

            return comment;
        });
    }

    public static Comment create(User user, Weibo weibo, CommentCreateModel model,String ipAddr) {
        return Hib.query(session -> {
            Comment comment = new Comment(user, weibo, model, ipAddr);
            session.save(comment);

//            session.load(user,user.getId());
            //评论,活跃度+1
//            UserInfo userInfo = user.getUserInfo();
//            userInfo.setActive(userInfo.getActive() + 1);
//            session.saveOrUpdate(user);
            //微博被评论的数量+1
            weibo.setComment(weibo.getComment() + 1);
            session.saveOrUpdate(weibo);

            return comment;
        });
    }

    public static Atme createAtme(User user, Weibo weibo, Comment comment) {
        return Hib.query(session -> {
            Atme atme = new Atme(user, weibo, comment);
            session.save(atme);
            return atme;
        });
    }
}

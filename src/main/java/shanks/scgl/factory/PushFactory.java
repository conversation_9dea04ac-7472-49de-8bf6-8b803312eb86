package shanks.scgl.factory;

import shanks.scgl.bean.api.base.PushCard;
import shanks.scgl.bean.card.*;
import shanks.scgl.bean.db.Message;
import shanks.scgl.bean.db.PushHistory;
import shanks.scgl.bean.db.User;
import shanks.scgl.utils.*;

import java.util.List;

/**
 * 消息存储于处理的工具类
 */
public class PushFactory {
    //发送一条消息,并在当前的发送历史中存储记录
    public static void pushNewMessage(User sender, Message message) {
        if (sender == null || message == null) {
            return;
        }

        //消息卡片,用于发送
        MessageCard messageCard = new MessageCard(message);
        String entity = TextUtil.toJson(messageCard);

        MiPushDispatcher miDispatcher = new MiPushDispatcher();

//        if (message.getGroupId() == 0) {
        //给朋友发
        User receiver = UserFactory.findById(message.getReceiverId());
        if (receiver == null) {
            return;
        }
        //历史记录
        PushHistory pushHistory = new PushHistory();
        pushHistory.setEntityType(PushCard.ENTITY_TYPE_MESSAGE);
        pushHistory.setEntity(entity);
        pushHistory.setReceiver(receiver);
        pushHistory.setReceiverPushId(receiver.getPushId());
        //保存到数据库
        Hib.queryOnly(session -> session.save(pushHistory));

        //推送的真实Model
        PushCard pushModel = new PushCard(pushHistory.getId());
        //每一条历史记录都是独立的,可以单独发送
        pushModel.add(pushHistory.getEntityType(), pushHistory.getEntity());

        //小米推送
        miDispatcher.sendMessage(sender.loadInfo().getUserName() + " 的消息", message.getContent(), receiver, pushModel,
                MiPushDispatcher.IMPORTANT, PushCard.ENTITY_TYPE_MESSAGE);
    }

    /**
     * 推送账号退出消息.不保存到数据库中,仅进行推送.
     *
     * @param receiver 接收者
     */
    static void pushLogout(User receiver) {

        PushCard pushModel = new PushCard("").add(PushCard.ENTITY_TYPE_LOGOUT, "Account logout");

        MiPushDispatcher miDispatcher = new MiPushDispatcher();
        miDispatcher.sendMessage("退出登录", "账号已在其他设备登录", receiver, pushModel,
                MiPushDispatcher.NORMAL, PushCard.ENTITY_TYPE_LOGOUT);
    }

    /**
     * 给一个朋友推送我的信息过去,类型是我关注了他
     *
     * @param receiver 接收者
     * @param userCard 我的卡片信息
     */
    public static void pushNewFans(User receiver, UserCard userCard) {
        String entity = TextUtil.toJson(userCard);

        PushHistory history = new PushHistory();

        history.setEntityType(PushCard.ENTITY_TYPE_NEW_FANS);
        history.setEntity(entity);
        history.setReceiver(receiver);
        history.setReceiverPushId(receiver.getPushId());
        Hib.queryOnly(session -> session.save(history));
        PushCard pushModel = new PushCard(history.getId()).add(history.getEntityType(), history.getEntity());

        MiPushDispatcher miDispatcher = new MiPushDispatcher();
        miDispatcher.sendMessage("新粉丝", userCard.getName() + " 关注了您",
                receiver, pushModel, MiPushDispatcher.NORMAL, PushCard.ENTITY_TYPE_NEW_FANS);
    }

    /**
     * 新的被评论
     *
     * @param receiver    接收者
     * @param commentCard Comment Card
     */
    public static void pushNewComment(User sender, User receiver, CommentCard commentCard) {
        String entity = TextUtil.toJson(commentCard);
        PushHistory history = new PushHistory();

        history.setEntityType(PushCard.ENTITY_TYPE_NEW_COMMENT);
        history.setEntity(entity);
        history.setReceiver(receiver);
        history.setReceiverPushId(receiver.getPushId());
        Hib.queryOnly(session -> session.save(history));
        PushCard pushModel = new PushCard(history.getId()).add(history.getEntityType(), history.getEntity());

        MiPushDispatcher miDispatcher = new MiPushDispatcher();
        miDispatcher.sendMessage(sender.loadInfo().getUserName() + " 的评论", commentCard.getContent(),
                receiver, pushModel, MiPushDispatcher.NORMAL, PushCard.ENTITY_TYPE_NEW_COMMENT);
    }

    public static void pushNewAtme(User sender, User receiver, CommentCard commentCard) {
        String entity = TextUtil.toJson(commentCard);

        PushHistory history = new PushHistory();

        history.setEntityType(PushCard.ENTITY_TYPE_NEW_ATME);
        history.setEntity(entity);
        history.setReceiver(receiver);
        history.setReceiverPushId(receiver.getPushId());
        Hib.queryOnly(session -> session.save(history));
        PushCard pushModel = new PushCard(history.getId()).add(history.getEntityType(), history.getEntity());

        MiPushDispatcher miDispatcher = new MiPushDispatcher();
        miDispatcher.sendMessage(sender.loadInfo().getUserName() + " @您", commentCard.getContent(),
                receiver, pushModel, MiPushDispatcher.NORMAL, PushCard.ENTITY_TYPE_NEW_ATME);
    }

    public static void pushNewZan(User sender, User receiver, FavorCard favorCard) {
        String entity = TextUtil.toJson(favorCard);
        PushHistory history = new PushHistory();

        history.setEntityType(PushCard.ENTITY_TYPE_NEW_ZAN);
        history.setEntity(entity);
        history.setReceiver(receiver);
        history.setReceiverPushId(receiver.getPushId());
        Hib.queryOnly(session -> session.save(history));
        PushCard pushModel = new PushCard(history.getId()).add(history.getEntityType(), history.getEntity());

        MiPushDispatcher miDispatcher = new MiPushDispatcher();
        miDispatcher.sendMessage("新的赞", sender.loadInfo().getUserName() + " 赞了您的作品",
                receiver, pushModel, MiPushDispatcher.NORMAL, PushCard.ENTITY_TYPE_NEW_ZAN);
    }

    public static void pushNewReco(User sender, User receiver, RecoCard recoCard) {
        String entity = TextUtil.toJson(recoCard);
        PushHistory history = new PushHistory();

        history.setEntityType(PushCard.ENTITY_TYPE_NEW_RECO);
        history.setEntity(entity);
        history.setReceiver(receiver);
        history.setReceiverPushId(receiver.getPushId());
        Hib.queryOnly(session -> session.save(history));
        PushCard pushModel = new PushCard(history.getId()).add(history.getEntityType(), history.getEntity());

        MiPushDispatcher miDispatcher = new MiPushDispatcher();
        miDispatcher.sendMessage(sender.loadInfo().getUserName() + " 的推荐", recoCard.getContent(),
                receiver, pushModel, MiPushDispatcher.NORMAL, PushCard.ENTITY_TYPE_NEW_RECO);
    }


    public static void pushOpenVIP(User receiver, UserCard userCard) {
        String entity = TextUtil.toJson(userCard);

        PushHistory history = new PushHistory();

        history.setEntityType(PushCard.ENTITY_TYPE_OPEN_VIP);
        history.setEntity(entity);
        history.setReceiver(receiver);
        history.setReceiverPushId(receiver.getPushId());
        Hib.queryOnly(session -> session.save(history));
        PushCard pushModel = new PushCard(history.getId()).add(history.getEntityType(), history.getEntity());

        MiPushDispatcher miDispatcher = new MiPushDispatcher();
        miDispatcher.sendMessage("VIP开通成功", "感谢您对平台的支持!",
                receiver, pushModel, MiPushDispatcher.NORMAL, PushCard.ENTITY_TYPE_OPEN_VIP);
    }

    /**
     * 新的被评分
     *
     * @param receiver    接收者
     * @param markCard Comment Card
     */
    public static void pushNewMark(User sender, User receiver, MarkCard markCard) {
        String entity = TextUtil.toJson(markCard);
        PushHistory history = new PushHistory();

        history.setEntityType(PushCard.ENTITY_TYPE_NEW_MARK);
        history.setEntity(entity);
        history.setReceiver(receiver);
        history.setReceiverPushId(receiver.getPushId());
        Hib.queryOnly(session -> session.save(history));
        PushCard pushModel = new PushCard(history.getId()).add(history.getEntityType(), history.getEntity());

        MiPushDispatcher miDispatcher = new MiPushDispatcher();
        miDispatcher.sendMessage(sender.loadInfo().getUserName() + " 评论了您的格律", markCard.getContent(),
                receiver, pushModel, MiPushDispatcher.NORMAL, PushCard.ENTITY_TYPE_NEW_MARK);
    }

    public static PushHistory findById(String id) {
        return Hib.query(session -> session.get(PushHistory.class, id));
    }

    @SuppressWarnings("unchecked")
    public static List<PushHistory> getUnArrives(User user) {
        return Hib.query(session -> (List<PushHistory>) session
                .createQuery("from PushHistory where receiverId =:uid and arrivalAt is null order by createAt desc ")
                .setParameter("uid", user.getId())
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }
}

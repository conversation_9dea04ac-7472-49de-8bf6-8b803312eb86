package shanks.scgl.factory;

import shanks.scgl.bean.db.User;
import shanks.scgl.bean.db.Visitor;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.List;

public class VisitFactory {
    public static Visitor find(User visited, User visitor) {
        return Hib.query(session -> (Visitor) session
                .createQuery("from Visitor where uid=:visitedId and vid=:visitorId")
                .setParameter("visitedId", visited.getId())
                .setParameter("visitorId", visitor.getId())
                .uniqueResult());
    }

    public static Visitor find(int visitedId, int visitorId) {
        return Hib.query(session -> (Visitor) session
                .createQuery("from Visitor where uid=:visitedId and vid=:visitorId")
                .setParameter("visitedId", visitedId)
                .setParameter("visitorId", visitorId)
                .uniqueResult());
    }

    public static Visitor visit(Visitor recorder, User visited, User visitor) {
        return Hib.query(session -> {
            Visitor vis = recorder;
            if (vis == null) {
                vis = new Visitor(visited, visitor);
                vis.setTimes(1);
            } else {
                vis.setTimes(vis.getTimes() + 1);
            }
            session.saveOrUpdate(vis);
            session.flush();
            //紧接着从数据库中查询出来
            session.refresh(vis);
            return vis;
        });
    }

    /**
     * 指定用户的被访问记录
     * @param visited 被访问者
     * @param min 开始时间
     * @param max 结束时间
     * @return 访问者列表
     */
    @SuppressWarnings("unchecked")
    public static List<Visitor> visitors(User visited, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Visitor>) session
                .createQuery("from Visitor where uid=:visitedId and updateAt>:min and updateAt<:max order by updateAt desc")
                .setParameter("visitedId", visited.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    //指定用户访问别人的记录
    @SuppressWarnings("unchecked")
    public static List<Visitor> visits(User visitor, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Visitor>) session
                .createQuery("from Visitor where vid=:visitorId and updateAt>:min and updateAt<:max order by updateAt desc")
                .setParameter("visitorId", visitor.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }
}

package shanks.scgl.factory;

import shanks.scgl.bean.db.*;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.List;

public class FavorFactory {
    /**
     * 获取指定微博的收藏记录
     */
    public static List<Keep> keeps(int wid, LocalDateTime min, LocalDateTime max) {
        //noinspection unchecked
        return Hib.query(session -> (List<Keep>) session
                .createQuery("from Keep where wid=:wid and createAt>:min and createAt<:max order by id desc ")
                .setParameter("wid", wid)
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_MAX_SIZE)
                .list());
    }

    public static List<Share> shares(int wid, LocalDateTime min, LocalDateTime max) {
        //noinspection unchecked
        return Hib.query(session -> (List<Share>) session
                .createQuery("from Share where wid=:wid and createAt>:min and createAt<:max order by id desc ")
                .setParameter("wid", wid)
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_MAX_SIZE)
                .list());
    }

    public static List<Favor> zans(int wid, LocalDateTime min, LocalDateTime max) {
        //noinspection unchecked
        return Hib.query(session -> (List<Favor>) session
                .createQuery("from Favor where wid=:wid and createAt>:min and createAt<:max order by id desc ")
                .setParameter("wid", wid)
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_MAX_SIZE)
                .list());
    }

    public static List<Favor> zanMe(User self, LocalDateTime min, LocalDateTime max) {
        //noinspection unchecked
        return Hib.query(session -> (List<Favor>) session
                .createQuery("from Favor where wuid=:wuid and createAt>:min and createAt<:max order by id desc ")
                .setParameter("wuid", self.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static List<Favor> meZan(User self, LocalDateTime min, LocalDateTime max) {
        //noinspection unchecked
        return Hib.query(session -> (List<Favor>) session
                .createQuery("from Favor where uid=:uid and createAt>:min and createAt<:max order by id desc ")
                .setParameter("uid", self.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static Keep getKeep(int kid) {
        return Hib.query(session -> session.get(Keep.class, kid));
    }

    public static Favor getZan(int zid) {
        return Hib.query(session -> session.get(Favor.class, zid));
    }

    /**
     * 指定用户是否收藏了微博
     */
    public static Keep getKeep(final Weibo weibo, final User self) {
        return Hib.query(session -> (Keep) session
                .createQuery("from Keep where wid=:wid and uid=:uid")
                .setParameter("wid", weibo.getId())
                .setParameter("uid", self.getId())
                .setMaxResults(1)
                .uniqueResult());
    }

    public static Keep keep(final Weibo weibo, final User self, final Anthology anthology) {
        Keep keep = getKeep(weibo, self);
        if (keep != null) {
            return keep;
        }

        return Hib.query(session -> {
            Keep kp = new Keep();
            kp.setUser(self);
            kp.setWeibo(weibo);
            kp.setWeiboUser(weibo.getUser());
            kp.setUid(self.getId());
            kp.setWid(weibo.getId());
            kp.setWuid(weibo.getUid());
            kp.setAnthId(anthology.getId());
            kp.setAnthology(anthology);

            session.save(kp);
            //更新收藏数量
            weibo.setKeep(weibo.getKeep() + 1);
            session.saveOrUpdate(weibo);
            return kp;
        });
    }

    public static Keep unKeep(final Weibo weibo, final Keep keep) {
        if (keep == null) {
            return null;
        }
        return Hib.query(session -> {
            session.delete(keep);
            //更新收藏数量
            weibo.setKeep(weibo.getKeep() - 1);
            session.saveOrUpdate(weibo);
            return keep;
        });
    }

    public static Favor getOpusZan(final Weibo weibo, final User self) {
        return Hib.query(session -> (Favor) session
                .createQuery("from Favor where wid=:wid and uid=:uid")
                .setParameter("wid", weibo.getId())
                .setParameter("uid", self.getId())
                .setMaxResults(1)
                .uniqueResult());
    }

    public static Favor zan(final Weibo weibo, final User self) {
        Favor opusZan = getOpusZan(weibo, self);
        if (opusZan != null) {
            return opusZan;
        }

        return Hib.query(session -> {
            Favor zan = new Favor();

            User weiboUser = session.get(User.class, weibo.getUid());
            //后面发消息的时候要用,关联一下
            weibo.setUser(weiboUser);
            session.load(self,self.getId());

            zan.setUser(self);
            zan.setWeibo(weibo);
            zan.setWeiboUser(weiboUser);
            zan.setUid(self.getId());
            zan.setWid(weibo.getId());
            zan.setWuid(weibo.getUid());
            session.save(zan);
            //更新推荐数量
            weibo.setZan(weibo.getZan() + 1);
            session.saveOrUpdate(weibo);

            //更新人气+1
            UserInfo weiboUserInfo=weiboUser.getUserInfo();
            weiboUserInfo.setTalent(weiboUserInfo.getTalent() + 1);
            session.saveOrUpdate(weiboUser);

            //更新积分-1,贡献+1
            UserInfo selfUserInfo=self.getUserInfo();
            selfUserInfo.setPoint(selfUserInfo.getPoint() - 1);
            selfUserInfo.setContribute(selfUserInfo.getContribute() + 1);
            session.saveOrUpdate(self);

            return zan;
        });
    }

    public static Favor unZan(final Weibo weibo, final Favor zan) {
        if (zan == null) {
            return null;
        }

        return Hib.query(session -> {
            session.delete(zan);
            //更新推荐数量
            weibo.setZan(weibo.getZan() - 1);
            session.saveOrUpdate(weibo);
            return zan;
        });
    }

    @SuppressWarnings("unchecked")
    public static List<Share> getShares(final Weibo weibo, final User self, final String type) {
        return Hib.query(session -> (List<Share>) session
                .createQuery("from Share where wid=:wid and uid=:uid and type=:type")
                .setParameter("wid", weibo.getId())
                .setParameter("uid", self.getId())
                .setParameter("type", type)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static Share share(final Weibo weibo, final User self, String type) {
        List<Share> shares = getShares(weibo, self, type);
        int contribute = 1;
        if (shares != null && shares.size() > 0) {
            contribute = 0;
        }

        int finalContribute = contribute;
        return Hib.query(session -> {
            Share share = new Share();
            User weiboUser = weibo.getUser();
            share.setUser(self);
            share.setWeibo(weibo);
            share.setWeiboUser(weiboUser);
            share.setUid(self.getId());
            share.setWid(weibo.getId());
            share.setWuid(weibo.getUid());
            share.setType(type);
            share.setContribute(finalContribute);
            session.save(share);
            //更新分享数量
            weibo.setShare(weibo.getShare() + 1);
            session.saveOrUpdate(weibo);

            //贡献+1
            if (finalContribute > 0) {
                session.load(self,self.getId());
                UserInfo selfUserInfo=self.getUserInfo();
                selfUserInfo.setContribute(selfUserInfo.getContribute() + finalContribute);
                session.saveOrUpdate(self);
            }
            return share;
        });
    }
}

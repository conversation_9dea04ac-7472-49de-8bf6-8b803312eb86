package shanks.scgl.factory;

import com.google.common.base.Strings;
import org.hibernate.query.NativeQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.LocalDateTimeType;
import org.hibernate.type.StandardBasicTypes;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.*;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;
import shanks.scgl.utils.TextUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * @version 1.0 2017年9月20日
 */
public class UserFactory {
    /**
     * 通过token查询用户信息
     */
    public static User findByToken(String token) {
        return Hib.query(session -> (User) session
                .createQuery("from User where token=:inToken")
                .setParameter("inToken", token)
                .uniqueResult());
    }

    /**
     * 通过account查询用户信息
     */
    public static User findByAccount(String account) {
        return Hib.query(session -> (User) session
                .createQuery("from User where account=:inAccount")
                .setParameter("inAccount", account)
                .uniqueResult());
    }

    /**
     * 通过phone找到User
     *
     * @param phone phone
     * @return User
     */
    public static User findByPhone(String phone) {
        return Hib.query(session -> (User) session
                .createQuery("from User where phone=:inPhone")
                .setParameter("inPhone", phone)
                .uniqueResult());
    }

    /**
     * 通过邮箱来找到user,作为注册的一种可选形式,用的比较少
     *
     * @param email email
     * @return User
     */
    public static User findByEmail(String email) {
        return Hib.query(session -> (User) session
                .createQuery("from User where email=:inEmail")
                .setParameter("inEmail", email)
                .uniqueResult());
    }

    public static User findByQQ(String openId) {
        return Hib.query(session -> (User) session
                .createQuery("from User where qqId=:openId")
                .setParameter("openId", openId)
                .uniqueResult());
    }

    public static User findByWX(String openId) {
        return Hib.query(session -> (User) session
                .createQuery("from User where wxId=:openId")
                .setParameter("openId", openId)
                .uniqueResult());
    }

    public static User findBySina(String uid) {
        return Hib.query(session -> (User) session
                .createQuery("from User where sinaId=:uid")
                .setParameter("uid", uid)
                .uniqueResult());
    }

    /**
     * 通过用户名找到User.昵称也是唯一的
     *
     * @param name user name
     * @return User
     */
    public static User findByName(String name) {
        return Hib.query(session -> (User) session
                .createQuery("from User where userInfo.username=:inName")
                .setParameter("inName", name)
                .uniqueResult());
//        return Hib.query(session -> {
//            UserInfo userInfo = (UserInfo) session
//                    .createQuery("FROM UserInfo where username=:inName")
//                    .setParameter("inName", name)
//                    .uniqueResult();
//            if (userInfo != null) {
//                return session.get(User.class, userInfo.getUid());
//            } else {
//                return null;
//            }
//        });
    }

    /**
     * 通过id查找用户
     *
     * @param id id
     * @return User
     */
    public static User findById(int id) {
        //通过Id查询更方便
        return Hib.query(session -> session.get(User.class, id));
    }

    /**
     * 更新用户信息到数据库
     *
     * @param user User
     * @return User
     */
    public static User update(User user) {
        return Hib.query(session -> {
            session.saveOrUpdate(user);
            return user;
        });
    }

    /**
     * 给当前的账号绑定pushId
     *
     * @param user   当前用户
     * @param pushId 设备的push id
     * @return User
     */
    public static User bindPushId(User user, String pushId) {
        //查询是否有其他账号绑定了这个设备,如果有则取消已有的绑定,避免混乱
        Hib.queryOnly(session -> {
            @SuppressWarnings("unchecked")
            List<User> userList = (List<User>) session
                    .createQuery("from User where lower(pushId)=:pushId and id!=:userId")
                    .setParameter("pushId", pushId.toLowerCase())
                    .setParameter("userId", user.getId())
                    .list();

            for (User u : userList) {
                u.setPushId(null);
                session.saveOrUpdate(u);
            }
        });

        if (pushId.equalsIgnoreCase(user.getPushId())) {
            //之前已经绑定,不需要再次绑定
            return user;
        } else {
            //当前账户之前已经绑定其他设备
            if (!Strings.isNullOrEmpty(user.getPushId())) {
                PushFactory.pushLogout(user);
            }

            //更新设备Id
            user.setPushId(pushId);
            return update(user);
        }
    }

    public static User bindQQ(User user, String openId) {
        //查询是否有其他账号绑定了这个QQ
        Hib.queryOnly(session -> {
            @SuppressWarnings("unchecked")
            List<User> userList = (List<User>) session
                    .createQuery("from User where qqId=:openId and id!=:userId")
                    .setParameter("openId", openId)
                    .setParameter("userId", user.getId())
                    .list();

            for (User u : userList) {
                u.setQqId(null);
                session.saveOrUpdate(u);
            }
        });

        user.setQqId(openId);
        return update(user);
    }

    /**
     * 绑定微信账号
     * 
     * @param user 用户
     * @param openId 微信OpenID
     * @return 更新后的用户
     */
    public static User bindWX(User user, String openId) {
        //查询是否有其他账号绑定了这个微信
        Hib.queryOnly(session -> {
            @SuppressWarnings("unchecked")
            List<User> userList = (List<User>) session
                    .createQuery("from User where wxId=:openId and id!=:userId")
                    .setParameter("openId", openId)
                    .setParameter("userId", user.getId())
                    .list();

            for (User u : userList) {
                u.setWxId(null);
                session.saveOrUpdate(u);
            }
        });

        user.setWxId(openId);
        return update(user);
    }

    public static User bindSina(User user, String openId) {
        //查询是否有其他账号绑定了这个微博账号
        Hib.queryOnly(session -> {
            @SuppressWarnings("unchecked")
            List<User> userList = (List<User>) session
                    .createQuery("from User where sinaId=:openId and id!=:userId")
                    .setParameter("openId", openId)
                    .setParameter("userId", user.getId())
                    .list();

            for (User u : userList) {
                u.setSinaId(null);
                session.saveOrUpdate(u);
            }
        });

        user.setSinaId(openId);
        return update(user);
    }

    public static User bindEmail(User user, String email) {
        //查询是否有其他账号绑定了这个微博账号
        Hib.queryOnly(session -> {
            @SuppressWarnings("unchecked")
            List<User> userList = (List<User>) session
                    .createQuery("from User where email=:email and id!=:userId")
                    .setParameter("email", email)
                    .setParameter("userId", user.getId())
                    .list();

            for (User u : userList) {
                u.setEmail(null);
                session.saveOrUpdate(u);
            }
        });

        user.setEmail(email);
        return update(user);
    }

    public static User bindPhone(User user, String phone) {
        //查询是否有其他账号绑定了这个手机号
        Hib.queryOnly(session -> {
            @SuppressWarnings("unchecked")
            List<User> userList = (List<User>) session
                    .createQuery("from User where phone=:phone and id!=:userId")
                    .setParameter("phone", phone)
                    .setParameter("userId", user.getId())
                    .list();

            for (User u : userList) {
                u.setPhone(null);
                session.saveOrUpdate(u);
            }
        });

        user.setPhone(phone);
        return update(user);
    }

    /**
     * 使用账户和密码进行登录
     */
    public static User login(String account, String password, boolean encrypted) {
        String accountStr = account.trim();
        String encodePassword = encrypted ? password : encodePassword(account, password);

        User user = Hib.query(session -> (User) session
                .createQuery("from User where account=:account and password=:password")
                .setParameter("account", accountStr)
                .setParameter("password", encodePassword)
                .uniqueResult());

        if (user != null) {
            user = login(user);
        }

        return user;
    }

    /**
     * 用户注册,注册的数据写入数据库,并返回数据库中的User数据
     *
     * @param account  账户
     * @param password 密码
     * @param name     用户名
     * @param phone    手机号
     * @return User
     */
    public static User register(String account, String password, String name, String phone) {
        //去除账号中的首位空格
        account = account.trim();
        password = encodePassword(account, password);

        User user = createUser(account, password, name, phone);
        if (user != null) {
            user = login(user);
        }
        return user;
    }

    /**
     * 注册第三方账号,qq,sina,微信等
     *
     * @param account  账户
     * @param password 密码
     * @param name     用户名
     * @param qqId     QQID
     * @param sinaId   弃用，兼容旧版API，总为null
     * @return User
     */
    public static User register3rd(String account, String password, String name, String qqId, String sinaId) {
        account = account.trim();
        password = encodePassword(account, password);
        User user = createUser(account, password, name, qqId, sinaId);
        if (user != null) {
            user = login(user);
        }
        return user;
    }

    /**
     * 注册V3版本,仅用于V3.0 API
     * @param account 账户
     * @param qqId QQID
     * @param wxId 微信ID
     * @return User
     */
    public static User registerV3(String account,String qqId,String wxId){
        account = account.trim();
        String password = encodePassword(account, UUID.randomUUID().toString());
        User user = createUser(account, password, account, qqId, wxId);
        if (user != null) {
            user = login(user);
        }
        return user;
    }

    private static User createUser(String accountStr, String password, String name, String qqId, String wxId) {
        User user = new User();
        user.setAccount(accountStr);
        user.setPassword(password);
        user.setQqId(qqId);
        user.setWxId(wxId);

        UserInfo userInfo = new UserInfo();
        userInfo.setUserName(name);

        user.setUserInfo(userInfo);

        return save(user, userInfo);
    }

    private static User save(User user, UserInfo userInfo) {
        return Hib.query(session -> {
            session.save(user);
            userInfo.setUid(user.getId());
            userInfo.setPoint(10);
            session.save(user);
            return user;
        });
    }

    /**
     * 注册部分的新建用户逻辑
     *
     * @param accountStr 账号
     * @param password   加密后的密码
     * @param name       用户名
     * @param phone      手机号
     * @return 返回一个用户
     */
    private static User createUser(String accountStr, String password, String name, String phone) {
        User user = new User();
        user.setAccount(accountStr);
        user.setPassword(password);
        user.setPhone(phone);

        UserInfo userInfo = new UserInfo();
        userInfo.setUserName(name);

        user.setUserInfo(userInfo);

        return save(user, userInfo);
    }

    /**
     * 登录操作.本质上是对token进行操作
     *
     * @param user user
     * @return User
     */
    public static User login(User user) {
        //使用一个随机的UUID值作为Token
        String token = UUID.randomUUID().toString();
        token = TextUtil.encodeBase64(token);
        user.setToken(token);

        return update(user);
    }


    public static boolean checkPassword(User user, String password) {
        return encodePassword(user.getAccount(), password).equals(user.getPassword());
    }

    /**
     * 密码加密
     *
     * @param account  账号
     * @param password 密码
     * @return 账号+密码取hash
     */
    private static String encodePassword(String account, String password) {
        //去除空格
        account = account.trim();
        password = password.trim();
        //账号+密码取hash
        password = TextUtil.getMD5(account + password);
        return password;
    }

    /**
     * 查询通讯录,在查询的时候使用联合查询,直接计算是否关注和是否是粉丝.
     * 尝试了比较多的方法,这种性能最好,但是维护起来稍微麻烦,比如增加字段的时候
     *
     * @param self 当前用户
     * @param min  follow表中的最小id
     * @param max  follow表中的最大id
     * @return List<UserCard>
     */
    @SuppressWarnings("unchecked")
    public static List<UserCard> contacts(User self, int min, int max) {
        return Hib.query(session -> {
            String sql = "SELECT user.id,username AS name,face AS portrait,intro,sex,su.updateAt AS modifyAt,su" +
                    ".fans,su.follow,weibo,birthday,location,contribute,talent,active,point,banner,vipTime,anthCount," +
                    " " +
                    "IFNULL((SELECT sb.id FROM scgl.scgl_blacklist AS sb WHERE sb.uid=:userId AND sb.black=su.id),0) " +
                    "AS " +
                    "blackId," +
                    "sf.id AS followId," +
                    "IFNULL((SELECT sf.id FROM scgl.scgl_follow AS sf WHERE sf.follow=:userId AND sf.fans=su.id),0) " +
                    "AS fansId  FROM scgl.scgl_follow sf,scgl.scgl_userinfo su ,scgl.scgl_user user WHERE sf" +
                    ".fans=:userId AND sf.follow=su" +
                    ".uid AND su.uid=user.id AND sf.id>:min AND sf.id<:max ORDER BY sf.id DESC ";
            NativeQuery query = session.createNativeQuery(sql);
            query.setParameter("userId", self.getId());
            query.setParameter("min", min);
            query.setParameter("max", max == 0 ? Integer.MAX_VALUE : max);
            query.setMaxResults(Constant.PAGE_SIZE);

            setUserCardScalar(query);
            //noinspection deprecation
            query.setResultTransformer(Transformers.aliasToBean(UserCard.class));
            return (List<UserCard>) query.list();
        });
    }

    @SuppressWarnings("unchecked")
    public static List<UserCard> fans(User self, int min, int max) {
        return Hib.query(session -> {
            String sql = "SELECT user.id,username AS name,face AS portrait,intro,sex,su.updateAt AS modifyAt,su" +
                    ".fans,su.follow,weibo,birthday,location,contribute,talent,active,point,banner,vipTime,anthCount," +
                    " " +
                    "IFNULL((SELECT sb.id FROM scgl.scgl_blacklist AS sb WHERE sb.uid=:userId AND sb.black=su.id),0) " +
                    "AS " +
                    "blackId," +
                    "sf.id AS fansId," +
                    "IFNULL((SELECT sf.id FROM scgl.scgl_follow AS sf WHERE sf.fans=:userId AND sf.follow=su.id),0) " +
                    "AS followId FROM scgl.scgl_follow sf,scgl.scgl_userinfo su,scgl.scgl_user user WHERE sf" +
                    ".follow=:userId AND sf" +
                    ".fans=su.uid AND su.uid=user.id AND sf.id>:min AND sf.id<:max ORDER BY sf.id DESC";
            NativeQuery query = session.createNativeQuery(sql);
            query.setParameter("userId", self.getId());
            query.setParameter("min", min);
            query.setParameter("max", max == 0 ? Integer.MAX_VALUE : max);
            query.setMaxResults(Constant.PAGE_SIZE);

            setUserCardScalar(query);
            //noinspection deprecation
            query.setResultTransformer(Transformers.aliasToBean(UserCard.class));
            return (List<UserCard>) query.list();
        });

    }

    public static void setUserCardScalar(NativeQuery query) {
        query.addScalar("id", StandardBasicTypes.INTEGER);
        query.addScalar("name", StandardBasicTypes.STRING);
        query.addScalar("portrait", StandardBasicTypes.STRING);
        query.addScalar("intro", StandardBasicTypes.STRING);
        query.addScalar("sex", StandardBasicTypes.STRING);
        query.addScalar("modifyAt", LocalDateTimeType.INSTANCE);
        query.addScalar("fans", StandardBasicTypes.INTEGER);
        query.addScalar("follow", StandardBasicTypes.INTEGER);
        query.addScalar("weibo", StandardBasicTypes.INTEGER);
        query.addScalar("birthday", StandardBasicTypes.STRING);
        query.addScalar("location", StandardBasicTypes.STRING);
        query.addScalar("contribute", StandardBasicTypes.INTEGER);
        query.addScalar("talent", StandardBasicTypes.INTEGER);
        query.addScalar("active", StandardBasicTypes.INTEGER);
        query.addScalar("point", StandardBasicTypes.INTEGER);
        query.addScalar("followId", StandardBasicTypes.INTEGER);
        query.addScalar("fansId", StandardBasicTypes.INTEGER);
        query.addScalar("vipTime", LocalDateTimeType.INSTANCE);
        query.addScalar("banner", StandardBasicTypes.STRING);
        query.addScalar("anthCount", StandardBasicTypes.INTEGER);
        query.addScalar("blackId", StandardBasicTypes.INTEGER);
    }

    /**
     * 关注人的操作
     *
     * @param origin 发起者
     * @param target 被关注的人
     * @return 被关注的人的信息
     */
    public static User follow(final User origin, final User target) {
        UserFollow follow = getUserFollow(origin, target);
        if (follow != null) {
            //已经关注,直接返回.
            return target;
        }
        return Hib.query(session -> {
            //想要操作懒加载的数据,需要重新load一次
            session.load(origin, origin.getId());
            session.load(target, target.getId());

            //我关注人的时候,我关注的数量+1,被关注人的粉丝数量+1
            UserFollow originFollow = new UserFollow();
            originFollow.setOrigin(origin);
            originFollow.setTarget(target);
            session.save(originFollow);

            UserInfo originUser = origin.getUserInfo();
            UserInfo targetUser = target.getUserInfo();
            originUser.setFollow(originUser.getFollow() + 1);
            targetUser.setFans(targetUser.getFans() + 1);

            session.saveOrUpdate(origin);
            session.saveOrUpdate(target);

            return target;
        });
    }

    /**
     * 取消关注
     *
     * @param origin 发起人
     * @param target 被取消的人
     * @return 被取消关注的人的信息
     */
    public static User unFollow(final User origin, final User target) {
        UserFollow follow = getUserFollow(origin, target);
        if (follow == null) {
            //没有关注,直接返回
            return target;
        }

        return Hib.query(session -> {
            session.load(origin, origin.getId());
            session.load(target, target.getId());
            //删掉记录
            session.delete(follow);

            UserInfo originUser = origin.getUserInfo();
            UserInfo targetUser = target.getUserInfo();
            originUser.setFollow(originUser.getFollow() - 1);
            targetUser.setFans(targetUser.getFans() - 1);

            session.saveOrUpdate(origin);
            session.saveOrUpdate(target);

            return target;
        });
    }


    public static User black(final User origin, final User target) {
        return Hib.query(session -> {
            Blacklist blacklist = new Blacklist();
            blacklist.setOrigin(origin);
            blacklist.setTarget(target);
            session.saveOrUpdate(blacklist);

            return target;
        });
    }

    public static User unBlack(final User origin, final User target) {
        Blacklist blacklist = getUserBlack(origin, target);
        if (blacklist == null) {
            return target;
        }

        return Hib.query(session -> {
            session.delete(blacklist);
            return target;
        });
    }

    /**
     * 查询两个人是否已经关注
     *
     * @param origin 发起者
     * @param target 被关注人
     * @return 返回中间类UserFollow
     */
    @SuppressWarnings("WeakerAccess")
    public static UserFollow getUserFollow(final User origin, final User target) {
        return Hib.query(session -> (UserFollow) session
                .createQuery("from UserFollow where fans=:originId and follow=:targetId")
                .setParameter("originId", origin.getId())
                .setParameter("targetId", target.getId())
                .setMaxResults(1)
                .uniqueResult());
    }

    public static UserFollow getUserFollow(final int originId, final int targetId) {
        return Hib.query(session -> (UserFollow) session
                .createQuery("from UserFollow where fans=:originId and follow=:targetId")
                .setParameter("originId", originId)
                .setParameter("targetId", targetId)
                .setMaxResults(1)
                .uniqueResult());
    }

    @SuppressWarnings("unchecked")
    public static List<Integer> getFollowIds(int fansId) {
        return Hib.query(session -> session
                .createQuery("select follow from UserFollow where fans=:fansId")
                .setParameter("fansId", fansId)
                .list());
    }

    @SuppressWarnings("WeakerAccess")
    public static Blacklist getUserBlack(final User origin, final User target) {
        return Hib.query(session -> (Blacklist) session
                .createQuery("from Blacklist where uid=:originId and black=:targetId")
                .setParameter("originId", origin.getId())
                .setParameter("targetId", target.getId())
                .setMaxResults(1)
                .uniqueResult());
    }

    @SuppressWarnings("WeakerAccess")
    public static Blacklist getUserBlack(final int originId, final int targetId) {
        return Hib.query(session -> (Blacklist) session
                .createQuery("from Blacklist where uid=:originId and black=:targetId")
                .setParameter("originId", originId)
                .setParameter("targetId", targetId)
                .setMaxResults(1)
                .uniqueResult());
    }

    /**
     * 搜索联系人的实现
     */
    @SuppressWarnings("unchecked")
    public static List<UserCard> search(User self, String name) {
        if (Strings.isNullOrEmpty(name)) {
            name = "";
        }
        final String searchName = "%" + name + "%";

        return Hib.query(session -> {
            String sql = "SELECT user.id,username AS name,face AS portrait,intro,sex,su.updateAt AS modifyAt,su" +
                    ".fans,su.follow,weibo,birthday,location,contribute,talent,active,point,banner,vipTime,anthCount," +
                    " IFNULL((SELECT sb.id FROM scgl.scgl_blacklist AS sb WHERE sb.uid=:userId AND sb.black=su.id),0)" +
                    " AS blackId,IFNULL((SELECT sf.id FROM" +
                    " scgl.scgl_follow AS sf WHERE sf.fans=:userId AND sf.follow=su.id),0) AS followId," +
                    "IFNULL((SELECT sf.id FROM scgl.scgl_follow AS sf WHERE sf.fans=su.id AND sf.follow=:userId),0) " +
                    "AS fansId FROM scgl.scgl_userinfo su ,scgl.scgl_user user WHERE user.id=su.uid AND su.username " +
                    "LIKE :keyName";
            NativeQuery query = session.createNativeQuery(sql);
            query.setParameter("userId", self.getId());
            query.setParameter("keyName", searchName);
            query.setMaxResults(Constant.PAGE_SIZE);

            setUserCardScalar(query);
            //noinspection deprecation
            query.setResultTransformer(Transformers.aliasToBean(UserCard.class));
            return (List<UserCard>) query.list();
        });
    }

    /**
     * 更新用户密码
     */
    public static User updatePwd(User self, String pwd) {
        String password = encodePassword(self.getAccount(), pwd);
        self.setPassword(password);
        return Hib.query(session -> {
            session.saveOrUpdate(self);
            return self;
        });
    }

    /**
     * 加载UserInfo信息
     *
     * @param user User
     * @return 关联的UserInfo
     */
    public static UserInfo getUserInfo(User user) {
        return Hib.query(session -> session.get(UserInfo.class, user.getId()));
    }

    /**
     * 是否是VIP
     *
     * @param user 指定用户
     * @return true is VIP
     */
    public static boolean isVIP(User user) {
        return user.getVipTime().isAfter(LocalDateTime.now());
    }

    public static Launch findLaunch(String id) {
        return Hib.query(session -> session.get(Launch.class, id));
    }

    public static Launch saveLaunch(Launch launch) {
        return Hib.query(session -> {
            session.saveOrUpdate(launch);
            return launch;
        });
    }

    public static Sign findSign(User user) {
        return Hib.query(session -> (Sign) session
                .createQuery("from Sign where uid=:uid")
                .setParameter("uid", user.getId())
                .uniqueResult());
    }

    public static Sign saveSign(User user, Sign sign, int score) {
        return Hib.query(session -> {
            session.saveOrUpdate(sign);

            session.load(user, user.getId());
            UserInfo userInfo = user.getUserInfo();
            userInfo.setPoint(userInfo.getPoint() + score);
            userInfo.setActive(userInfo.getActive() + score);
            session.saveOrUpdate(user);
            return sign;
        });
    }

    public static int getFansId(User self, User user) {
        int fansId = 0;
        UserFollow fans = UserFactory.getUserFollow(user, self);
        if (fans != null) {
            fansId = fans.getId();
        }
        return fansId;
    }

    public static int getFollowId(User self, User user) {
        int followId = 0;
        UserFollow follow = UserFactory.getUserFollow(self, user);
        if (follow != null) {
            followId = follow.getId();
        }
        return followId;
    }

    public static int getBlackId(User self, User user) {
        int blackId = 0;
        Blacklist blacklist = UserFactory.getUserBlack(self, user);
        if (blacklist != null) {
            blackId = blacklist.getId();
        }
        return blackId;
    }
}

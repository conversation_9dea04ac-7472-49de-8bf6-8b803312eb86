package shanks.scgl.factory;

import org.hibernate.query.NativeQuery;
import org.hibernate.transform.Transformers;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.Feedback;
import shanks.scgl.bean.db.User;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.List;

public class SocialFactory {


    @SuppressWarnings("unchecked")
    public static List<UserCard> talent(User self, LocalDateTime min, LocalDateTime max) {
        //很耗时,后面想办法优化
        return Hib.query(session -> {
            String sql = "SELECT suser.id,username AS name,face AS portrait,intro,sex,su.updateAt AS modifyAt,su" +
                    ".fans,su.follow,weibo,birthday,location,contribute,talent,active,point,banner,vipTime,anthCount," +
                    "IFNULL((SELECT sb.id FROM scgl.scgl_blacklist AS sb WHERE sb.uid=:uid AND sb.black=su.id),0) AS " +
                    "blackId,IFNULL((SELECT sf.id FROM scgl.scgl_follow AS sf WHERE sf.fans=:uid AND sf.follow=su" +
                    ".id),0) AS followId,IFNULL((SELECT sf.id FROM scgl.scgl_follow AS sf WHERE sf.fans=su.id AND sf" +
                    ".follow=:uid),0) AS fansId FROM scgl.scgl_userinfo su ,scgl.scgl_user suser WHERE suser.id=su" +
                    ".uid AND talent > 100 AND su.updateAt>:min AND su.updateAt<:max ORDER BY su.updateAt DESC";
            NativeQuery query = session.createNativeQuery(sql);
            query.setParameter("uid", self.getId());
            query.setParameter("min", min);
            query.setParameter("max", max);
            query.setMaxResults(Constant.PAGE_SIZE);

            UserFactory.setUserCardScalar(query);
            //noinspection deprecation
            query.setResultTransformer(Transformers.aliasToBean(UserCard.class));
            return (List<UserCard>) query.list();
        });
    }


    @SuppressWarnings("unchecked")
    public static List<UserCard> vips(User self, LocalDateTime min, LocalDateTime max) {

        return Hib.query(session -> {
            String sql = "SELECT suser.id,username AS name,face AS portrait,intro,sex,su.updateAt AS modifyAt,su" +
                    ".fans,su.follow,weibo,birthday,location,contribute,talent,active,point,banner,vipTime,anthCount," +
                    "IFNULL((SELECT sb.id FROM scgl.scgl_blacklist AS sb WHERE sb.uid=:uid AND sb.black=su.id),0) AS " +
                    "blackId,IFNULL((SELECT sf.id FROM scgl.scgl_follow AS sf WHERE sf.fans=:uid AND sf.follow=su" +
                    ".id),0) AS followId,IFNULL((SELECT sf.id FROM scgl.scgl_follow AS sf WHERE sf.fans=su.id AND sf" +
                    ".follow=:uid),0) AS fansId FROM scgl.scgl_userinfo su ,scgl.scgl_user suser WHERE suser.id=su" +
                    ".uid AND vipTime>now() AND su.updateAt>:min AND su.updateAt<:max ORDER BY su.updateAt DESC";
            NativeQuery query = session.createNativeQuery(sql);
            query.setParameter("uid", self.getId());
            query.setParameter("min", min);
            query.setParameter("max", max);
            query.setMaxResults(Constant.PAGE_SIZE);

            UserFactory.setUserCardScalar(query);
            //noinspection deprecation
            query.setResultTransformer(Transformers.aliasToBean(UserCard.class));
            return (List<UserCard>) query.list();
        });
    }

//    public static List<UserCard> fresher(User self, LocalDateTime min, LocalDateTime max) {
//
//        return Hib.query(session -> {
//            String sql = "SELECT suser.id,username AS name,face AS portrait,intro,sex,su.updateAt AS modifyAt,su" +
//                    ".fans,su.follow,weibo,birthday,location,contribute,talent,active,point,banner,vipTime," +
//                    "(SELECT count(*) FROM scgl.scgl_anthology sa WHERE sa.uid=su.id AND isDelete=0 AND type=0) AS " +
//                    "anthCount,IFNULL((SELECT sf.id FROM scgl.scgl_follow AS sf WHERE sf.fans=:uid AND sf.follow=su" +
//                    ".id),0) AS followId,IFNULL((SELECT sf.id FROM scgl.scgl_follow AS sf WHERE sf.fans=su.id AND
// sf" +
//                    ".follow=:uid),0) AS fansId FROM scgl.scgl_userinfo su ,scgl.scgl_user suser WHERE suser.id=su" +
//                    ".uid AND suser.createAt>:min AND suser.createAt<:max ORDER BY id DESC";
//            NativeQuery query = session.createNativeQuery(sql);
//            query.setParameter("uid", self.getId());
//            query.setParameter("min", min);
//            query.setParameter("max", max);
//            query.setMaxResults(Constant.PAGE_SIZE);
//
//            UserFactory.setUserCardScalar(query);
//            //noinspection deprecation
//            query.setResultTransformer(Transformers.aliasToBean(UserCard.class));
//            return (List<UserCard>) query.list();
//        });
//    }

    @SuppressWarnings("Duplicates")
    public static Feedback saveFeedback(Feedback feedBack) {
        return Hib.query(session -> {
            session.saveOrUpdate(feedBack);
            session.flush();
            session.refresh(feedBack);
            return feedBack;
        });
    }
}

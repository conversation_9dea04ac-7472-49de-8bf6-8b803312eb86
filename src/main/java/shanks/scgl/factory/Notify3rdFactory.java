package shanks.scgl.factory;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.google.common.base.Strings;
import shanks.scgl.bean.db.IdentityCode;
import shanks.scgl.utils.Hib;
import shanks.scgl.utils.TextUtil;

import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Date;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

@SuppressWarnings("SpellCheckingInspection")
public class Notify3rdFactory {
    //产品名称:云通信短信API产品,开发者无需替换
    private static final String product = "Dysmsapi";
    //产品域名,开发者无需替换
    private static final String domain = "dysmsapi.aliyuncs.com";
    private static final String accessKeyId = "LTAINWAl3gwwJvRP";
    private static final String accessKeySecret = "PAxTfKrgQ8vClJbwOCeJPjQXxfatv5";

    //EMAIL发送相关
    private static final String emailAccount = "<EMAIL>";
    private static final String emailPassword = "9834jfjfdaklmnfqp";
    private static final String emailSMTPHost = "smtp.exmail.qq.com";

    public static IdentityCode sendIcSms(String phone, int type) {
        if (Strings.isNullOrEmpty(phone)) {
            return null;
        }

        try {
            String code = TextUtil.getIdentifyCode();

            String chinaPhone = phone;
            if (phone.contains(" ") && phone.split(" ").length == 2) {
                chinaPhone = phone.split(" ")[1];
            }

            SendSmsResponse response = sendSms(chinaPhone, "SMS_113445531", code);
            IdentityCode identityCode = new IdentityCode();
            identityCode.setCode(code);
            identityCode.setIdentify(phone);
            identityCode.setBizId(response.getBizId());
            identityCode.setRequestId(response.getRequestId());
            identityCode.setResponseCode(response.getCode());
            identityCode.setMessage(response.getMessage());
            identityCode.setType(type);

            identityCode = Notify3rdFactory.saveIdentifyCode(identityCode);
            return identityCode;
        } catch (Exception ex) {
            return null;
        }
    }

    public static void sendOpenVIP(String phone, int length) {
        if (Strings.isNullOrEmpty(phone)) {
            return;
        }

        try {
            String chinaPhone = phone;
            if (phone.contains(" ") && phone.split(" ").length == 2) {
                chinaPhone = phone.split(" ")[1];
            }

            sendSms(chinaPhone, "SMS_123669614", String.valueOf(length));
        } catch (Exception ignored) {
        }
    }

    public static IdentityCode sendIcEmail(String email, String name, int type) {
        try {
            String code = TextUtil.getIdentifyCode();
            sendEmail(email, name, code);

            IdentityCode identityCode = new IdentityCode();
            identityCode.setCode(code);
            identityCode.setIdentify(email);
            identityCode.setBizId("");
            identityCode.setRequestId("");
            identityCode.setResponseCode("");
            identityCode.setMessage("OK");
            identityCode.setType(type);

            identityCode = Notify3rdFactory.saveIdentifyCode(identityCode);
            return identityCode;
        } catch (Exception ex) {
            Logger.getLogger("Notify3rd").log(Level.WARNING, ex.getMessage());
            return null;
        }
    }

    private static SendSmsResponse sendSms(String phone, String template, String code) throws ClientException {

        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        //初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
        DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
        IAcsClient acsClient = new DefaultAcsClient(profile);

        //组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
        //必填:待发送手机号
        request.setPhoneNumbers(phone);
        //必填:短信签名-可在短信控制台中找到
        request.setSignName("诗词格律");
        //必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(template);
        //可选:模板中的变量替换JSON串
        request.setTemplateParam("{\"code\":\"" + code + "\"}");

        //选填-上行短信扩展码(无特殊需求用户请忽略此字段)
        //request.setSmsUpExtendCode("90997");

        //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
        //request.setOutId("yourOutId");

        //hint 此处可能会抛出异常，注意catch
        return acsClient.getAcsResponse(request);
    }

    private static void sendEmail(String toAddress, String name, String code) throws Exception {

        //TODO 不再使用发邮件，删除代码
        //Security.addProvider(new com.sun.net.ssl.internal.ssl.Provider());
        final String SSL_FACTORY = "javax.net.ssl.SSLSocketFactory";

        Properties props = new Properties();
        props.setProperty("mail.transport.protocol", "smtp");
        props.setProperty("mail.smtp.host", emailSMTPHost);
        props.setProperty("mail.smtp.auth", "true");

        props.setProperty("mail.smtp.socketFactory.class", SSL_FACTORY);
        props.setProperty("mail.smtp.socketFactory.fallback", "false");
        //邮箱发送服务器端口,这里设置为465端口
        props.setProperty("mail.smtp.port", "465");
        props.setProperty("mail.smtp.socketFactory.port", "465");

        Session session = javax.mail.Session.getInstance(props);
        session.setDebug(false);
        MimeMessage message = new MimeMessage(session);
        message.setFrom(new InternetAddress(emailAccount, "诗词格律", "UTF-8"));
        message.setRecipient(MimeMessage.RecipientType.TO,
                new InternetAddress(toAddress, name, "UTF-8"));
        message.setSubject("验证码", "UTF-8");
        String content = "尊敬的诗友!<br /> 您的验证码为:" + code + ",该验证码10分钟内有效,请勿泄露予他人!";
        message.setContent(content, "text/html;charset=UTF-8");
        message.setSentDate(new Date());
        message.saveChanges();
        Transport transport = session.getTransport();
        transport.connect(emailAccount, emailPassword);
        transport.sendMessage(message, message.getAllRecipients());
        transport.close();
    }

    private static IdentityCode saveIdentifyCode(IdentityCode code) {
        return Hib.query(session -> {
            session.save(code);
            return code;
        });
    }

    public static IdentityCode findByIdentify(String identify) {
        return Hib.query(session -> (IdentityCode) session
                .createQuery("from IdentityCode where identify=:inPhone order by createAt desc ")
                .setParameter("inPhone", identify)
                .setMaxResults(1)
                .uniqueResult());
    }

}

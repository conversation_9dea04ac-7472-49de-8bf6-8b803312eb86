package shanks.scgl.factory;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.weibo.WeiboCreateModel;
import shanks.scgl.bean.db.*;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("unchecked")
public class WeiboFactory {
    @SuppressWarnings("unchecked")
    //获取指定时间段内新增的微博数据
    public static List<Weibo> circle(User self, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> {
            //采用在数据库里联合的形式,时间控制在30ms左右,如果是先查所有的关注者再用in的方式,时间是40ms左右
            return (List<Weibo>) session
                    .createQuery("from Weibo where exists (select 1 from UserFollow where fans=:fansId and " +
                            "follow=uid) and createAt>:min and createAt<:max order by id desc ")
                    .setParameter("fansId", self.getId())
                    .setParameter("min", min)
                    .setParameter("max", max)
                    .setMaxResults(Constant.PAGE_SIZE)
                    .list();
        });
    }

    @SuppressWarnings("unchecked")
    public static List<Weibo> list(User user, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Weibo>) session
                .createQuery("from Weibo where uid =:uid and createAt>:min and createAt<:max order by id desc ")
                .setParameter("uid", user.getId())
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    @SuppressWarnings("unchecked")
    public static List<Weibo> list(int type, LocalDateTime min, LocalDateTime max) {
        return Hib.query(session -> (List<Weibo>) session
                .createQuery("from Weibo where type=:type and createAt>:min and createAt<:max order by id desc ")
                .setParameter("type", type)
                .setParameter("min", min)
                .setParameter("max", max)
                .setMaxResults(Constant.PAGE_SIZE)
                .list());
    }

    public static List<Weibo> list(ArrayList<Integer> ids) {
        return Hib.query(session -> (List<Weibo>) session
                .createQuery("from Weibo where id in (:ids) order by id desc ")
                .setParameter("ids", ids)
                .list());
    }

    /**
     * 通过id找weibo
     *
     * @param id id
     * @return weibo
     */
    public static Weibo findById(int id) {
        return Hib.query(session -> session.get(Weibo.class, id));
    }

    /**
     * 通过poem id进行查找
     */
    public static Weibo findByPoemId(String poemId) {
        return Hib.query(session -> (Weibo) session
                .createQuery("from Weibo where poemId=:poemId")
                .setParameter("poemId", poemId)
                .uniqueResult());
    }

    /**
     * 创建一条微博
     */
    public static Weibo create(User user, Anthology anth, WeiboCreateModel model,String ipAddr) {
        return Hib.query(session -> {
            Weibo weibo = new Weibo(user, anth, model,ipAddr);
            session.save(weibo);
            //微博数量+1,贡献+1
            session.load(user, user.getId());
            UserInfo userInfo = user.getUserInfo();
            userInfo.setWeibo(userInfo.getWeibo() + 1);
            userInfo.setContribute(userInfo.getContribute() + 1);
            session.saveOrUpdate(user);
            return weibo;
        });
    }

    public static Weibo delete(Weibo weibo, User self) {
        return Hib.query(session -> {
            session.delete(weibo);

            session.load(self, self.getId());
            UserInfo selfUserInfo = self.getUserInfo();
            selfUserInfo.setWeibo(selfUserInfo.getWeibo() - 1);
            selfUserInfo.setContribute(selfUserInfo.getContribute() - 1);
            session.saveOrUpdate(self);

            session.createQuery("delete from Atme where wid=:wid")
                   .setParameter("wid", weibo.getId())
                   .executeUpdate();
            session.createQuery("delete from Comment where wid=:wid")
                   .setParameter("wid", weibo.getId())
                   .executeUpdate();
            session.createQuery("delete from Keep where wid=:wid")
                   .setParameter("wid", weibo.getId())
                   .executeUpdate();
            session.createQuery("delete from Favor where wid=:wid")
                   .setParameter("wid", weibo.getId())
                   .executeUpdate();
            session.createQuery("delete from Share where wid=:wid")
                   .setParameter("wid", weibo.getId())
                   .executeUpdate();

            return weibo;
        });
    }

    /**
     * 更新一条微博
     */
    public static Weibo update(Weibo weibo, WeiboCreateModel model, Anthology anthology,String ipAddr) {
        return Hib.query(session -> {
            weibo.update(model, anthology,ipAddr);
            session.saveOrUpdate(weibo);
            return weibo;
        });
    }

    @Deprecated
    public static Weibo bind(Weibo weibo, String PoemId) {
        return Hib.query(session -> {
            weibo.setPoemId(PoemId);
            session.saveOrUpdate(weibo);
            return weibo;
        });
    }

    public static void addViewCount(List<Weibo> weibos) {
        if (weibos == null || weibos.size() == 0) {
            return;
        }

        Hib.queryOnly(session -> {
            ArrayList<Integer> ids = new ArrayList<>();
            for (Weibo weibo : weibos) {
                ids.add(weibo.getId());
            }

            session.createQuery("update Weibo set view=view+1 where id in (:ids)")
                   .setParameter("ids", ids)
                   .executeUpdate();
        });
    }

    public static void addViewCount(Weibo weibo) {
        Hib.queryOnly(session -> {
            weibo.setView(weibo.getView() + 1);
            session.saveOrUpdate(weibo);
        });
    }

    public static void addViewCount(int id) {
        Hib.queryOnly(session -> {
            session.createQuery("update Weibo set view=view+1 where id = :id")
                   .setParameter("id", id)
                   .executeUpdate();
        });
    }

    /**
     * 只能搜索最近的20条
     */
    @Deprecated
    public static List<Weibo> search(String keyWord) {
        if (Strings.isNullOrEmpty(keyWord)) {
            keyWord = "";
        }
        final String searchKey = "%" + keyWord + "%";

        return Hib.query(session -> {
            //noinspection unchecked
            return (List<Weibo>) session
                    .createQuery("from Weibo where content like :keyWord order by id desc ")
                    .setParameter("keyWord", searchKey)
                    .setMaxResults(20).list();
        });
    }

    public static List<Weibo> search(String keyWord, LocalDateTime minTime, LocalDateTime maxTime) {
        if (Strings.isNullOrEmpty(keyWord)) {
            keyWord = "";
        }
        final String searchKey = "%" + keyWord + "%";

        return Hib.query(session -> {
            //noinspection unchecked
            return (List<Weibo>) session
                    .createQuery("from Weibo where createAt>:minTime and createAt<:maxTime and content like :keyWord " +
                            "order by createAt desc ")
                    .setParameter("keyWord", searchKey)
                    .setParameter("maxTime", maxTime)
                    .setParameter("minTime", minTime)
                    .setMaxResults(20).list();
        });
    }

    public static List<Weibo> search(User user, String keyWord, LocalDateTime maxTime) {
        if (Strings.isNullOrEmpty(keyWord)) {
            keyWord = "";
        }
        final String searchKey = "%" + keyWord + "%";

        return Hib.query(session -> {
            //noinspection unchecked
            return (List<Weibo>) session
                    .createQuery("from Weibo where uid=:uid and createAt<:createAt and content like :keyWord order by" +
                            " createAt desc ")
                    .setParameter("uid", user.getId())
                    .setParameter("keyWord", searchKey)
                    .setParameter("createAt", maxTime)
                    .setMaxResults(20).list();
        });
    }

    @Deprecated
    public static List<Weibo> search(User self, String keyWord) {
        if (Strings.isNullOrEmpty(keyWord)) {
            keyWord = "";
        }
        final String searchKey = "%" + keyWord + "%";

        return Hib.query(session -> {
            //noinspection unchecked
            return (List<Weibo>) session
                    .createQuery("from Weibo where content like :keyWord and uid=:uid order by id desc ")
                    .setParameter("keyWord", searchKey)
                    .setParameter("uid", self.getId())
                    .setMaxResults(20).list();
        });
    }

}

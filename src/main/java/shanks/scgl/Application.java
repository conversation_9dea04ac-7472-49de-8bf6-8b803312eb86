package shanks.scgl;

import org.glassfish.jersey.server.ResourceConfig;
import shanks.scgl.provider.AuthRequestFilter;
import shanks.scgl.provider.GsonProvider;

import java.util.logging.Logger;

public class Application extends ResourceConfig {
    public Application() {
        //注册逻辑处理的包名
        packages("shanks.scgl.service");

        //全局拦截器
        register(AuthRequestFilter.class);

        //注册json解析器
        //register(JacksonJsonProvider.class);
        register(GsonProvider.class);

        //注册日志打印输出
        register(Logger.class);
    }
}

package shanks.scgl.utils;


import com.google.common.base.Strings;
import com.xiaomi.xmpush.server.Constants;
import com.xiaomi.xmpush.server.Message;
import com.xiaomi.xmpush.server.Sender;
import org.json.simple.parser.ParseException;
import shanks.scgl.bean.api.base.PushCard;
import shanks.scgl.bean.db.User;

import java.io.IOException;

/**
 * 小米推送
 */
public class MiPushDispatcher {
    public static final int NORMAL = 1;
    public static final int IMPORTANT = 7;
    private static final String PACKAGE_NAME = "shanks.scgl";
    @SuppressWarnings("SpellCheckingInspection")
    private static final String APP_SECRET_KEY = "xfR3WYLG+bmlXgDRpDpOgA==";

    public boolean sendMessage(String title, String description, User receiver, PushCard model,
                               int notifyType, int notifyId) {
        if (receiver == null || model == null) {
            return false;
        }

        //32位是个推的id,暂时忽略
        if (Strings.isNullOrEmpty(receiver.getPushId()) || receiver.getPushId().length() == 32) {
            return false;
        }

        String pushString = TextUtil.toJson(model);
        if (Strings.isNullOrEmpty(pushString)) {
            return false;
        }

        Constants.useOfficial();
        Sender sender = new Sender(APP_SECRET_KEY);
        Message message = new Message.Builder()
                .title(title)
                .description(description).payload(pushString)
                .restrictedPackageName(PACKAGE_NAME)
                .notifyType(notifyType)
                .notifyId(notifyId)
                .extra(Constants.EXTRA_PARAM_NOTIFY_FOREGROUND, "0")
                .build();
        try {
            sender.send(message, receiver.getPushId(), 3);
        } catch (IOException | ParseException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

}

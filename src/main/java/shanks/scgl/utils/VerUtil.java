package shanks.scgl.utils;

import java.io.IOException;
import java.util.Base64;

public class VerUtil {
    @SuppressWarnings("SpellCheckingInspection")
    public static String decryptOldId(String loginId) throws IOException {
        String key1= "shanks.shicigl.com";
        String key2= "DFDNLDAN2OFN405FND";

        byte[] key = (TextUtil.getMD5(key1) + TextUtil.getMD5(key2)).getBytes();
        byte[] cipherBytes = Base64.getDecoder().decode(loginId);

        for(int i=0;i<cipherBytes.length;i++) {
            cipherBytes[i] = (byte) (cipherBytes[i] ^ key[i % key.length]);
        }

        return new String(cipherBytes);
    }
}

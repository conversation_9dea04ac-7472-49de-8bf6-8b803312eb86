package shanks.scgl.utils;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import shanks.scgl.bean.card.OSSTCard;

@SuppressWarnings("SpellCheckingInspection")
public class OSSUtil {
    // 目前只有"cn-hangzhou"这个region可用, 不要使用填写其他region的值
    private static final String REGION_CN_HANGZHOU = "cn-hangzhou";
    private static final String STS_API_VERSION = "2015-04-01";
    private static final String ACCESS_KEY_ID = "LTAI84L3cfuGcfOE";
    private static final String ACCESS_KEY_SECRET = "JpH6tQ3mArDOj3512pr0jpWdw9rkdP";
    private static final String ROLE_ARN = "acs:ram::1754196581258299:role/aliyunosstokengeneratorrole";
//    private static final String ROLE_SESSION_NAME = "external-username";
    private static final long DURATION = 3600;

    private static final String policy = "{\n" +
            "  \"Statement\": [\n" +
            "    {\n" +
            "      \"Action\": [\n" +
            "        \"oss:GetObject\",\n" +
            "        \"oss:PutObject\",\n" +
            "        \"oss:ListParts\",\n" +
            "        \"oss:AbortMultipartUpload\",\n" +
            "        \"oss:ListObjects\"\n" +
            "      ],\n" +
            "      \"Effect\": \"Allow\",\n" +
            "      \"Resource\": [\"acs:oss:*:*:scgl-medias/*\", \"acs:oss:*:*:scgl-medias\"]\n" +
            "    }\n" +
            "  ],\n" +
            "  \"Version\": \"1\"\n" +
            "}";

    public static OSSTCard getToken(String ruleSessionName) {
        try {
            // 创建一个 Aliyun Acs Client, 用于发起 OpenAPI 请求
            IClientProfile profile = DefaultProfile.getProfile(REGION_CN_HANGZHOU, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
            DefaultAcsClient client = new DefaultAcsClient(profile);

            // 创建一个 AssumeRoleRequest 并设置请求参数
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setVersion(STS_API_VERSION);
            request.setMethod(MethodType.POST);
            request.setProtocol(ProtocolType.HTTPS);

            request.setRoleArn(ROLE_ARN);
            request.setRoleSessionName(ruleSessionName);
            request.setPolicy(policy);
            request.setDurationSeconds(DURATION);

            // 发起请求，并得到response
            final AssumeRoleResponse response = client.getAcsResponse(request);

            OSSTCard card = new OSSTCard();
            AssumeRoleResponse.Credentials credentials = response.getCredentials();
            card.setAccessKeyId(credentials.getAccessKeyId());
            card.setAccessKeySecret(credentials.getAccessKeySecret());
            card.setSecurityToken(credentials.getSecurityToken());
            card.setExpiration(credentials.getExpiration());

            return card;
        } catch (Exception ignore) {
            ignore.printStackTrace();
        }
        return null;
    }
}

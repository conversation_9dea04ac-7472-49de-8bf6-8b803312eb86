package shanks.scgl.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

public class TimeUtil {
    /**
     * 根据客户端传来的时间,判断最小时间.如果是0则从2014-9-4开始取数据,在这之前没有数据
     *
     * @param min 时间,最小单位毫秒
     * @return LocalDateTime
     */
    public static LocalDateTime getMinTime(long min) {
        LocalDateTime minTime;
        if (min == 0) {
            minTime = LocalDateTime.of(2014, 9, 4, 0, 0);
        } else {
            minTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(min), ZoneId.systemDefault());
        }

        return minTime;
    }

    /**
     * 最大时间如果是0,取当前时间
     *
     * @param max 最小单位毫秒
     * @return LocalDateTime
     */
    public static LocalDateTime getMaxTime(long max) {
        LocalDateTime maxTime;
        if (max == 0) {
            maxTime = LocalDateTime.now();
        } else {
            maxTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(max), ZoneId.systemDefault());
        }

        return maxTime;
    }
}

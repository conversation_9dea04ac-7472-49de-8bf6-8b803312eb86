package shanks.scgl.utils;

import shanks.scgl.bean.db.IdentityCode;
import shanks.scgl.factory.Notify3rdFactory;

import java.time.Duration;
import java.time.LocalDateTime;

public class ICodeUtil {
    public static boolean checkFrequent(String identify) {
        IdentityCode identityCode = Notify3rdFactory.findByIdentify(identify);
        //60秒内重复请求
        if (identityCode != null) {
            Duration duration = Duration.between(identityCode.getCreateAt(), LocalDateTime.now());
            if (duration.getSeconds() < 60) {
                return false;
            }
        }

        return true;
    }

    public static boolean checkIdentifyCode(IdentityCode identityCode, String code) {
        if (identityCode != null) {
            //验证码不匹配
            if (!identityCode.getCode().equals(code)) {
                return false;
            }

            Duration duration = Duration.between(identityCode.getCreateAt(), LocalDateTime.now());
            if (duration.toMinutes() > 10) {
                return false;
            }
        } else {
            return false;
        }

        return true;
    }

}

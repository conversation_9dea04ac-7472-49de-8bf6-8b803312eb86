package shanks.scgl.provider;

import com.google.common.base.Strings;
import org.glassfish.jersey.server.ContainerRequest;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.db.User;
import shanks.scgl.factory.UserFactory;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.SecurityContext;
import javax.ws.rs.ext.Provider;
import java.io.IOException;
import java.security.Principal;

/**
 * 用于所有请求的接口过滤和拦截
 */
@Provider
public class AuthRequestFilter implements ContainerRequestFilter {

    @Override
    public void filter(ContainerRequestContext requestContext) throws IOException {
        //检测是否是登录和注册的接口
        String relationPath = ((ContainerRequest) requestContext).getPath(false);
        if (relationPath.contains("account") || relationPath.contains("pub")) {
            //不做拦截
            return;
        }
        //得到token
        String token = requestContext.getHeaders().getFirst("token");
        if (!Strings.isNullOrEmpty(token)) {
            //查询用户的信息
            final User self = UserFactory.findByToken(token);
            //账号被锁定
            if (self != null && self.getLocked() == 0) {
                //给当前请求添加一个上下文
                requestContext.setSecurityContext(new SecurityContext() {
                    //主体部分
                    @Override
                    public Principal getUserPrincipal() {
                        //User实现Principal接口
                        return self;
                    }

                    @Override
                    public boolean isUserInRole(String s) {
                        //可以在这里写入用户的权限,role是权限名
                        //可以管理管理员权限
                        return true;
                    }

                    @Override
                    public boolean isSecure() {
                        //检查https,默认false
                        return false;
                    }

                    @Override
                    public String getAuthenticationScheme() {
                        //忽略
                        return null;
                    }
                });
                //写入上下文后返回
                return;
            }
        }

        ResponseModel model = ResponseModel.buildAccountError();
        //构建一个返回
        Response response = Response.status(Response.Status.OK).entity(model).build();
        //停止一个请求的继续下发,调用该方法后直接返回请求,不再调用Service
        requestContext.abortWith(response);
    }
}

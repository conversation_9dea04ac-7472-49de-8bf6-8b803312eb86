package shanks.scgl.service.v3;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.User;
import shanks.scgl.bean.db.UserInfo;
import shanks.scgl.bean.v3.api.user.UpdateUserInfoModel;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.service.BaseService;

import javax.ws.rs.Path;
import javax.ws.rs.PUT;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

@Path("/v3/user")
public class UserService extends BaseService {

    @PUT
    @Path("/info")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> updateInfo(UpdateUserInfoModel model) {
        if (!UpdateUserInfoModel.check(model)) {
            return ResponseModel.buildError("参数异常");
        }

        User user = getSelf();
        UserInfo userInfo = user.loadInfo();

        switch (model.getType()) {
            case UpdateUserInfoModel.TYPE_PORTRAIT:
                userInfo.setFace(model.getValue());
                break;
            case UpdateUserInfoModel.TYPE_NAME:
                //昵称进行修改
                if (!userInfo.getUserName().equals(model.getValue())) {
                    User tmp = UserFactory.findByName(model.getValue());
                    if (tmp != null) {
                        return ResponseModel.buildError("昵称已存在,请换一个");
                    }
                }
                userInfo.setUserName(model.getValue());
                break;
            case UpdateUserInfoModel.TYPE_SEX:
                userInfo.setSex(model.getValue());
                break;
            case UpdateUserInfoModel.TYPE_LOCATION:
                userInfo.setLocation(model.getValue());
                break;
            case UpdateUserInfoModel.TYPE_BIRTH:
                userInfo.setBirthday(model.getValue());
                break;
            case UpdateUserInfoModel.TYPE_INTRO:
                userInfo.setIntro(model.getValue());
                break;
        }

        user = UserFactory.update(user);
        UserCard userCard = new UserCard(user, 0, 0, 0);
        return ResponseModel.buildSuccess("更新成功", userCard);
    }

}

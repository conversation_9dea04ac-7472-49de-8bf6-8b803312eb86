package shanks.scgl.service;

import shanks.scgl.bean.db.User;

import javax.ws.rs.core.Context;
import javax.ws.rs.core.SecurityContext;

public class BaseService {
    //添加一个上下文注解，该注解会给securityContext赋值
    @Context
    protected SecurityContext securityContext;

    /**
     * 从上下文中获取当前用户
     * @return User
     */
    public User getSelf(){
        return (User) securityContext.getUserPrincipal();
    }
}

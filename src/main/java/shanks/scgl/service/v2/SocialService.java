package shanks.scgl.service.v2;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.api.social.FeedbackModel;
import shanks.scgl.bean.db.Feedback;
import shanks.scgl.factory.SocialFactory;
import shanks.scgl.service.BaseService;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@Path("/v2/social")
public class SocialService extends BaseService {
    @PUT
    @Path("/feedback")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel feedback(FeedbackModel model) {
        if (!FeedbackModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        Feedback feedback = new Feedback(model, getSelf());
        SocialFactory.saveFeedback(feedback);

        return ResponseModel.buildOk();
    }
}

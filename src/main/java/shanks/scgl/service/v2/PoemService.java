package shanks.scgl.service.v2;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.CountCard;
import shanks.scgl.bean.card.PoemCard;
import shanks.scgl.bean.db.Poem;
import shanks.scgl.factory.PoemFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Path("/v2/poem")
public class PoemService extends BaseService {
    /**
     * 按创作时间,从大到小取
     */
    @GET
    @Path("/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<PoemCard>> poems(@PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Poem> poems = PoemFactory.listByCreateAt(getSelf(), minTime, maxTime);
        List<PoemCard> cards = new ArrayList<>();
        for (Poem poem : poems) {
            cards.add(new PoemCard(poem, PoemCard.ACTION_NONE));
        }
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/count")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<CountCard> count() {
        Long count = PoemFactory.count(getSelf());
        return ResponseModel.buildOk(new CountCard(count));
    }
}

package shanks.scgl.service.v2;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.WeiboCard;
import shanks.scgl.bean.db.User;
import shanks.scgl.bean.db.Weibo;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.factory.WeiboFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Path("/v2/weibo")
public class WeiboService extends BaseService {
    /**
     * 搜索自己的作品
     */
    @GET
    @Path("/search/{uid}/{max}/{key:(.*)?}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboCard>> search(@DefaultValue("") @PathParam("key") String key,
                                                 @PathParam("uid") int uid, @PathParam("max") long max) {
        User user = UserFactory.findById(uid);
        if (user == null) {
            return ResponseModel.buildParameterError();
        }

        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
        //查找指定时间之后的匹配内容
        List<Weibo> weibos = WeiboFactory.search(user, key, maxTime);
        //添加浏览次数
        WeiboFactory.addViewCount(weibos);

        List<WeiboCard> cards = weibos.stream()
                                      .map(WeiboCard::new)
                                      .collect(Collectors.toList());
        //返回
        return ResponseModel.buildOk(cards);
    }

    /**
     * 搜索所有的作品
     */
    @GET
    @Path("/search/{max}/{key:(.*)?}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboCard>> search(@DefaultValue("") @PathParam("key") String key,
                                                 @PathParam("max") long max) {

        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
        LocalDateTime minTime = LocalDateTime.now().minusYears(1);
        //查找指定时间之后的匹配内容,最多搜索一年
        List<Weibo> weibos = WeiboFactory.search(key, minTime, maxTime);
        //添加浏览次数
        WeiboFactory.addViewCount(weibos);

        List<WeiboCard> cards = weibos.stream()
                                      .map(WeiboCard::new)
                                      .collect(Collectors.toList());
        //返回
        return ResponseModel.buildOk(cards);
    }
}

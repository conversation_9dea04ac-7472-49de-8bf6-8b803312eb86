package shanks.scgl.service.v2.pub;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.WeiboCard;
import shanks.scgl.bean.card.WeiboSampleCard;
import shanks.scgl.bean.db.Weibo;
import shanks.scgl.factory.WeiboFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Path("/v2/pub/weibo")
public class WeiboService extends BaseService {
    @GET
    @Path("/piazza/{type}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboSampleCard>> piazza(@PathParam("type") int type,
                                                       @PathParam("min") long min,
                                                       @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Weibo> weibos = WeiboFactory.list(type, minTime, maxTime);
//        List<WeiboSampleCard> cards = weibos.stream()
//                                            .map(WeiboSampleCard::new)
//                                            .collect(Collectors.toList());
        //临时解决方案，屏蔽此诗友发表的作品
        List<WeiboSampleCard> cards = new ArrayList<>();
        for (Weibo weibo : weibos) {
            if (weibo.getUid() == 56137) {
                continue;
            }
            WeiboSampleCard weiboSampleCard = new WeiboSampleCard(weibo);
            cards.add(weiboSampleCard);
        }

        WeiboFactory.addViewCount(weibos);

        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/{ids}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboCard>> weibos(@PathParam("ids") String ids) {
        if (Strings.isNullOrEmpty(ids)) {
            return ResponseModel.buildParameterError();
        }

        String[] idArray = ids.split(",");
        if (idArray.length == 0 || idArray.length > Constant.PAGE_SIZE) {
            return ResponseModel.buildParameterError();
        }

        ArrayList<Integer> idInts = new ArrayList<>();
        try {
            for (String s : idArray) {
                Integer id = Integer.parseInt(s);
                idInts.add(id);
            }
        } catch (Exception e) {
            //ignore
        }

        List<Weibo> weibos = WeiboFactory.list(idInts);
        List<WeiboCard> cards = new ArrayList<>();
        if (weibos != null) {
            cards = weibos.stream()
                          .map(WeiboCard::new)
                          .collect(Collectors.toList());
        }

        return ResponseModel.buildOk(cards);
    }
}

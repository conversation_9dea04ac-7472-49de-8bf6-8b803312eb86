package shanks.scgl.service.v2.pub;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.v2.CmtCard;
import shanks.scgl.bean.view.CmtView;
import shanks.scgl.factory.CmtFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Path("/v2/pub/cmt")
public class CmtService extends BaseService {

    @GET
    @Path("/opus/{id}/{time}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<CmtCard>> listOfOpus(@PathParam("id") int id, @PathParam("time") long time) {
        LocalDateTime maxTime = TimeUtil.getMaxTime(time);
        List<CmtView> comments = CmtFactory.listOfTop(id, maxTime);
        ArrayList<CmtCard> cards = new ArrayList<>();

        if (comments.size() > 0) {
            ArrayList<Integer> ids = new ArrayList<>();
            for (CmtView comment : comments) {
                ids.add(comment.getId());
                cards.add(new CmtCard(comment));
            }

            List<CmtView> children = CmtFactory.listOfChild(id, ids);

            if (children.size() > 0) {
                HashMap<Integer, ArrayList<CmtCard>> map = new HashMap<>();
                for (CmtView child : children) {
                    CmtCard cmtCard = new CmtCard(child);
                    ArrayList<CmtCard> cList = map.computeIfAbsent(child.getParent(), k -> new ArrayList<>());
                    cList.add(cmtCard);
                }

                for (CmtCard card : cards) {
                    ArrayList<CmtCard> child = map.get(card.getId());
                    if (child != null) {
                        card.setChildren(child);
                    }
                }
            }
        }

        return ResponseModel.buildOk(cards);
    }

}

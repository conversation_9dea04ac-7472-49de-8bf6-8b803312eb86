package shanks.scgl.service.v2.pub;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.v2.OpusCard;
import shanks.scgl.bean.view.OpusView;
import shanks.scgl.factory.OpusFactory;
import shanks.scgl.factory.WeiboFactory;
import shanks.scgl.service.BaseService;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@Path("/v2/pub/opus")
public class OpusService extends BaseService {

    @GET
    @Path("{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<OpusCard> opus(@PathParam("id") int id) {
        OpusView opusView = OpusFactory.findById(id);
        if (opusView == null) {
            return ResponseModel.buildNotFoundWeiboError(null);
        }
        WeiboFactory.addViewCount(opusView.getId());

        return ResponseModel.buildOk(new OpusCard(opusView));
    }

}

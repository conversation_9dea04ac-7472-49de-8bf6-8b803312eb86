package shanks.scgl.service.v2.pub;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.User;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.service.BaseService;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@Path("/v2/pub/user")
public class UserService extends BaseService {
    /**
     * 拉取指定用户的信息
     *
     * @param id 指定用户的id
     * @return ResponseModel<UserCard>
     */
    @GET
    @Path("{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> getUser(@HeaderParam("token") String token, @PathParam("id") int id) {
        User user = UserFactory.findById(id);
        if (user == null) {
            return ResponseModel.buildNotFoundUserError(null);
        }

        //没有登录
        if (Strings.isNullOrEmpty(token)) {
            return ResponseModel.buildOk(new UserCard(user));
        }
        User self = UserFactory.findByToken(token);
        if (self == null || self.getId() == id) {
            return ResponseModel.buildOk(new UserCard(user));
        } else {
            //我是否关注了TA
            int followId = UserFactory.getFollowId(self, user);
            //TA关注我的编号,0表示没有关注我
            int fansId = UserFactory.getFansId(self, user);

            int blackId = UserFactory.getBlackId(self, user);

            return ResponseModel.buildOk(new UserCard(user, followId, fansId, blackId));
        }
    }
}

package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.account.AccountRspModel;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.api.user.Bind3rdModel;
import shanks.scgl.bean.api.user.BindModel;
import shanks.scgl.bean.api.user.UpdateInfoModel;
import shanks.scgl.bean.card.SignCard;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.*;
import shanks.scgl.factory.Notify3rdFactory;
import shanks.scgl.factory.PushFactory;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.ICodeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;

@Path("/user")
public class UserService extends BaseService {
    @PUT
    //@Path("") 不需要写,就是当前目录 user
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> update(UpdateInfoModel model) {
        if (!UpdateInfoModel.check(model)) {
            return ResponseModel.buildParameterError();
        }

        User user = getSelf();

        //昵称进行修改
        if (!user.loadInfo().getUserName().equals(model.getName())) {
            User tmp = UserFactory.findByName(model.getName());
            if (tmp != null) {
                return ResponseModel.buildHaveNameError();
            }
        }

        //上面已经Load了,这个地方可以不load.这个操作用的应该不多.暂时先这样
        user = model.updateToUser(user);
        user = UserFactory.update(user);
        //更新的是自己
        UserCard userCard = new UserCard(user, 0, 0, 0);
        return ResponseModel.buildOk(userCard);
    }

    /**
     * 拉取联系人
     *
     * @return 联系人列表
     */
    @GET
    @Path("/contact/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<UserCard>> contact(@PathParam("min") int min, @PathParam("max") int max) {
        User self = getSelf();
        List<UserCard> cards = UserFactory.contacts(self, min, max);
        return ResponseModel.buildOk(cards);
    }

    /**
     * 粉丝列表
     */
    @GET
    @Path("/fans/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<UserCard>> fans(@PathParam("min") int min, @PathParam("max") int max) {
        User self = getSelf();
        List<UserCard> cards = UserFactory.fans(self, min, max);
        return ResponseModel.buildOk(cards);
    }

    /**
     * 关注人,修改使用PUT
     *
     * @param followUserId 被关注的人
     * @return 被关注人的信息
     */
    @PUT
    @Path("/follow/{followUserId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> follow(@PathParam("followUserId") int followUserId) {
        User self = getSelf();

        if (self.loadInfo().getFollow() >= Constant.MAX_FOLLOW) {
            return ResponseModel.buildTooManyFollowError();
        }

        //不能关注我自己
        if (self.getId() == followUserId) {
            return ResponseModel.buildParameterError();
        }
        //找到我要关注的人
        User followUser = UserFactory.findById(followUserId);
        if (followUser == null) {
            return ResponseModel.buildNotFoundUserError(null);
        }

        //对方是否已经将我拉黑
        Blacklist blacklist = UserFactory.getUserBlack(followUser, self);
        if (blacklist != null) {
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_IS_BLACK);
        }

        //备注默认没有,后面可以扩展
        followUser = UserFactory.follow(self, followUser);
        if (followUser == null) {
            //关注失败,返回服务器异常
            return ResponseModel.buildServiceError();
        }

        //我关注目标用户的id
        int followId = UserFactory.getFollowId(self, followUser);
        //目标用户关注我的id
        int fansId = UserFactory.getFansId(self, followUser);

        // 通知被关注的人,我关注了TA.给他发送一个我的信息过去
        PushFactory.pushNewFans(followUser, new UserCard(self, fansId, followId,
                UserFactory.getBlackId(followUser, self)));

        //返回关注的人的信息
        return ResponseModel.buildOk(new UserCard(followUser, followId, fansId,
                UserFactory.getBlackId(self, followUser)));
    }

    @DELETE
    @Path("/follow/{followId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> unFollow(@PathParam("followId") int followId) {
        User self = getSelf();

        //取消对自己的关注,操作无效
        if (self.getId() == followId) {
            return ResponseModel.buildOk(new UserCard(self, 0, 0, 0));
        }
        //找到我要关注的人
        User unFollowUser = UserFactory.findById(followId);
        if (unFollowUser == null) {
            return ResponseModel.buildNotFoundUserError(null);
        }

        unFollowUser = UserFactory.unFollow(self, unFollowUser);
        if (unFollowUser == null) {
            //关注失败,返回服务器异常
            return ResponseModel.buildServiceError();
        }

        //返回关注的人的信息
        return ResponseModel.buildOk(new UserCard(unFollowUser, 0
                , UserFactory.getFansId(self, unFollowUser)
                , UserFactory.getBlackId(self, unFollowUser)));
    }

    /**
     * 拉取指定用户的信息
     *
     * @param id 指定用户的id
     * @return ResponseModel<UserCard>
     */
    @Deprecated
    @GET
    @Path("{id}") //http://xxx/api/user/{id}
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> getUser(@PathParam("id") int id) {
        User self = getSelf();
        if (self.getId() == id) {
            //返回自己,不必查询数据库
            return ResponseModel.buildOk(new UserCard(self, 0, 0, 0));
        }

        User user = UserFactory.findById(id);
        if (user == null) {
            return ResponseModel.buildNotFoundUserError(null);
        }

        //我是否关注了TA
        int followId = UserFactory.getFollowId(self, user);
        //TA关注我的编号,0表示没有关注我
        int fansId = UserFactory.getFansId(self, user);

        int blackId = UserFactory.getBlackId(self, user);

        return ResponseModel.buildOk(new UserCard(user, followId, fansId, blackId));
    }

    /**
     * 将某人拉入黑名单
     */
    @PUT
    @Path("/black/{userId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> black(@PathParam("userId") int userId) {
        User self = getSelf();

        //不能将自己拉黑
        if (self.getId() == userId) {
            return ResponseModel.buildParameterError();
        }

        //找到我想拉黑的人
        User blackUser = UserFactory.findById(userId);
        if (blackUser == null) {
            return ResponseModel.buildNotFoundUserError(null);
        }


        //删除互相关系
        blackUser = UserFactory.unFollow(self, blackUser);
        UserFactory.unFollow(blackUser,self);


        //备注默认没有,后面可以扩展
        blackUser = UserFactory.black(self, blackUser);
        if (blackUser == null) {
            //关注失败,返回服务器异常
            return ResponseModel.buildServiceError();
        }

        //我关注目标用户的id
        int followId = UserFactory.getFollowId(self, blackUser);
        //目标用户关注我的id
        int fansId = UserFactory.getFansId(self, blackUser);
        //目标用户是否已经被我拉黑(这个地方肯定已经拉黑了)
        int blackId = UserFactory.getBlackId(self, blackUser);


        //返回关注的人的信息
        return ResponseModel.buildOk(new UserCard(blackUser, followId, fansId, blackId));
    }

    @DELETE
    @Path("/black/{userId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> unBlack(@PathParam("userId") int userId) {
        User self = getSelf();

        if (self.getId() == userId) {
            return ResponseModel.buildOk(new UserCard(self, 0, 0, 0));
        }

        User unBlackUser = UserFactory.findById(userId);
        if (unBlackUser == null) {
            return ResponseModel.buildNotFoundUserError(null);
        }

        unBlackUser = UserFactory.unBlack(self, unBlackUser);
        if (unBlackUser == null) {
            //关注失败,返回服务器异常
            return ResponseModel.buildServiceError();
        }

        //返回关注的人的信息
        return ResponseModel.buildOk(new UserCard(unBlackUser,
                UserFactory.getFollowId(self, unBlackUser),
                UserFactory.getFansId(self, unBlackUser), 0));
    }

    @GET //不涉及数据更改,只是查询,则为GET
    @Path("/search/{name:(.*)?}") //名字为任意字符,可以为空
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<UserCard>> search(@DefaultValue("") @PathParam("name") String name) {
        User self = getSelf();

        List<UserCard> userCards = UserFactory.search(self, name);

        //返回
        return ResponseModel.buildOk(userCards);
    }

    @PUT
    @Path("/bind3rd")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> bind(Bind3rdModel model) {
        if (!Bind3rdModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        User user = null;
        if (model.getType() == Bind3rdModel.TYPE_QQ) {
            user = UserFactory.bindQQ(getSelf(), model.getOpenId());
        } else if (model.getType() == Bind3rdModel.TYPE_SINA) {
            user = UserFactory.bindSina(getSelf(), model.getOpenId());
        }

        //判断异常情况
        if (user == null) {
            return ResponseModel.buildParameterError();
        }
        AccountRspModel rspModel = new AccountRspModel(user);
        return ResponseModel.buildOk(rspModel);
    }

    @POST
    @Path("/ic")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> getIdentifyCode(BindModel model) {
        if (Strings.isNullOrEmpty(model.getPhone()) && Strings.isNullOrEmpty(model.getEmail())) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        if (!Strings.isNullOrEmpty(model.getPhone())) {
            return getSms(model);
        } else if (!Strings.isNullOrEmpty(model.getEmail())) {
            return getEmail(model);
        }

        return ResponseModel.buildParameterError();
    }

    /**
     * 获取email验证码
     */
    private ResponseModel<AccountRspModel> getEmail(BindModel model) {
        User self = getSelf();
        //已经绑定
        if (model.getEmail().equals(self.getEmail())) {
            AccountRspModel rspModel = new AccountRspModel(self);
            return ResponseModel.buildOk(rspModel);
        }

        //发送前检查是否发送过于频繁
        if (!ICodeUtil.checkFrequent(model.getEmail())) {
            return ResponseModel.buildServiceBusyError();
        }

        //发送验证码
        if (Notify3rdFactory.sendIcEmail(model.getEmail(), self.loadInfo()
                                                               .getUserName(), IdentityCode.TYPE_BIND) != null) {
            return ResponseModel.buildOk();
        } else {
            return ResponseModel.buildServiceError();
        }
    }

    private ResponseModel<AccountRspModel> getSms(BindModel model) {
        User self = getSelf();
        //已经绑定
        if (model.getPhone().equals(self.getPhone())) {
            AccountRspModel rspModel = new AccountRspModel(self);
            return ResponseModel.buildOk(rspModel);
        }

        //发送前检查是否发送过于频繁
        if (!ICodeUtil.checkFrequent(model.getPhone())) {
            return ResponseModel.buildServiceBusyError();
        }

        //发送验证码
        if (Notify3rdFactory.sendIcSms(model.getPhone(), IdentityCode.TYPE_BIND) != null) {
            return ResponseModel.buildOk();
        } else {
            return ResponseModel.buildServiceError();
        }
    }

    @PUT
    @Path("/bind")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> bind(BindModel model) {
        if (!BindModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        if (!Strings.isNullOrEmpty(model.getPhone())) {
            return bindPhone(model);
        } else if (!Strings.isNullOrEmpty(model.getEmail())) {
            return bindEmail(model);
        }

        return ResponseModel.buildParameterError();
    }

    private ResponseModel<AccountRspModel> bindEmail(BindModel model) {
        //查找验证码并验证
        IdentityCode identityCode = Notify3rdFactory.findByIdentify(model.getEmail());
        if (!ICodeUtil.checkIdentifyCode(identityCode, model.getCode())) {
            return ResponseModel.buildIdentifyCodeError();
        }

        User user = UserFactory.bindEmail(getSelf(), model.getEmail());
        AccountRspModel rspModel = new AccountRspModel(user);
        return ResponseModel.buildOk(rspModel);
    }

    private ResponseModel<AccountRspModel> bindPhone(BindModel model) {
        //查找验证码并验证
        IdentityCode identityCode = Notify3rdFactory.findByIdentify(model.getPhone());
        if (!ICodeUtil.checkIdentifyCode(identityCode, model.getCode())) {
            return ResponseModel.buildIdentifyCodeError();
        }

        //验证通过
        User user = UserFactory.bindPhone(getSelf(), model.getPhone());
        AccountRspModel rspModel = new AccountRspModel(user);
        return ResponseModel.buildOk(rspModel);
    }

    @PUT
    @Path("/password/{pwd}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel changePwd(@PathParam("pwd") String pwd) {
        User self = getSelf();

        if (UserFactory.updatePwd(self, pwd) != null) {
            return ResponseModel.buildOk();
        } else {
            return ResponseModel.buildParameterError();
        }
    }

    @PUT
    @Path("/banner")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> updateBanner(String url) {
        User self = getSelf();
        if (!UserFactory.isVIP(self)) {
            // 判断是否是VIP
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_NOT_VIP);
        }
        url = url.replace("\"", "");

        self.loadInfo().setBanner(url);

        User user = UserFactory.update(self);
        return ResponseModel.buildOk(new UserCard(user));
    }

    @PUT
    @Path("/sign")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<SignCard> sign() {
        User self = getSelf();

        LocalDateTime today = LocalDateTime.now();
        String todayStr = String.format("%4d-%02d-%02d", today.getYear(), today.getMonthValue(), today.getDayOfMonth());

        Sign sign = UserFactory.findSign(self);

        if (sign == null) {
            sign = new Sign(self, todayStr);
            UserFactory.saveSign(self, sign, 1);
        } else if (!todayStr.equals(sign.getLastSign())) {
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            String yesterdayStr = String.format("%4d-%02d-%02d", yesterday.getYear(),
                    yesterday.getMonthValue(), yesterday.getDayOfMonth());

            int score;
            //昨天已经签到
            if (yesterdayStr.equals(sign.getLastSign())) {
                sign.setCount(sign.getCount() + 1);
                score = sign.getCount();
                if (score > 5) {
                    score = 5;
                }

                sign.setLastSign(todayStr);
            } else {
                //
                sign.setCount(1);
                score = 1;
                sign.setLastSign(todayStr);
            }

            //VIP双倍
            if (UserFactory.isVIP(self)) {
                score = score + score;
            }

            sign = UserFactory.saveSign(self, sign, score);
        }

        return ResponseModel.buildOk(new SignCard(sign, self));
    }

}

package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.account.*;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.IdentityCode;
import shanks.scgl.bean.db.Launch;
import shanks.scgl.bean.db.User;
import shanks.scgl.factory.AdminFactory;
import shanks.scgl.factory.Notify3rdFactory;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.ICodeUtil;
import shanks.scgl.utils.VerUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@Path("/account")
public class AccountService extends BaseService {
    //登录
    @POST
    @Path("/login")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> login(LoginModel model) {

        if (!LoginModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        String account = model.getAccount();
        if (Strings.isNullOrEmpty(account)) {
            User user = UserFactory.findByPhone(model.getPhone());
            if (user != null) {
                account = user.getAccount();
            } else {
                return ResponseModel.buildLoginError();
            }
        }

        User user = UserFactory.login(account, model.getPassword(), false);
        return bindAndReturn(user, model.getPushId(), ResponseModel.buildLoginError());
    }

    @POST
    @Path("/login3rd")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> login(Login3rdModel model) {
        if (!Login3rdModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        User user = null;
        //QQ登录
        if (model.getType() == Login3rdModel.TYPE_QQ) {
            user = UserFactory.findByQQ(model.getOpenId());
            if (user == null) {
                return ResponseModel.buildQqNotBindError();
            }
        } else if (model.getType() == Login3rdModel.TYPE_SINA) {
            user = UserFactory.findBySina(model.getOpenId());
            if (user == null) {
                return ResponseModel.buildSinaNotBindError();
            }
        }

        //判断异常情况
        if (user == null) {
            return ResponseModel.buildLoginError();
        }
        user = UserFactory.login(user);
        return bindAndReturn(user, model.getPushId(), ResponseModel.buildLoginError());
    }

    @POST
    @Path("/loginIc")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> login(LoginIcModel model) {
        if (!LoginIcModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        if (!Strings.isNullOrEmpty(model.getPhone())) {
            return loginSms(model);
        } else if (!Strings.isNullOrEmpty(model.getEmail())) {
            return loginEmail(model);
        }

        return ResponseModel.buildParameterError();
    }

    private ResponseModel<AccountRspModel> loginEmail(LoginIcModel model) {
        //查找验证码并验证
        IdentityCode identityCode = Notify3rdFactory.findByIdentify(model.getEmail());
        if (!ICodeUtil.checkIdentifyCode(identityCode, model.getCode())) {
            return ResponseModel.buildIdentifyCodeError();
        }

        //验证通过
        User user = UserFactory.findByEmail(model.getEmail());
        if (user == null) {
            return ResponseModel.buildLoginError();
        }

        //更新token
        user = UserFactory.login(user);
        return bindAndReturn(user, model.getPushId(), ResponseModel.buildLoginError());
    }

    private ResponseModel<AccountRspModel> loginSms(LoginIcModel model) {
        //查找验证码并验证
        IdentityCode identityCode = Notify3rdFactory.findByIdentify(model.getPhone());
        if (!ICodeUtil.checkIdentifyCode(identityCode, model.getCode())) {
            return ResponseModel.buildIdentifyCodeError();
        }

        //验证通过
        User user = UserFactory.findByPhone(model.getPhone());
        if (user == null) {
            return ResponseModel.buildLoginError();
        }

        //更新token
        user = UserFactory.login(user);
        return bindAndReturn(user, model.getPushId(), ResponseModel.buildLoginError());
    }

    @POST
    @Path("/ic")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> getIdentifyCode(LoginIcModel model) {
        if (Strings.isNullOrEmpty(model.getPhone()) && Strings.isNullOrEmpty(model.getEmail())) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        if (!Strings.isNullOrEmpty(model.getPhone())) {
            return getSms(model);
        } else if (!Strings.isNullOrEmpty(model.getEmail())) {
            return getEmail(model);
        }

        return ResponseModel.buildParameterError();
    }

    private ResponseModel<AccountRspModel> getEmail(LoginIcModel model) {
        User user = UserFactory.findByEmail(model.getEmail());
        if (user == null) {
            //邮箱没有绑定
            return ResponseModel.buildEmailNotBindError();
        }

        if (!Strings.isNullOrEmpty(model.getPushId()) && model.getPushId().equals(user.getPushId())) {
            user = UserFactory.login(user);
            AccountRspModel rspModel = new AccountRspModel(user);
            return ResponseModel.buildOk(rspModel);
        }

        //发送前检查是否发送过于频繁
        if (!ICodeUtil.checkFrequent(model.getEmail())) {
            return ResponseModel.buildServiceBusyError();
        }

        //发送验证码
        if (Notify3rdFactory.sendIcEmail(model.getEmail(), user.loadInfo()
                                                               .getUserName(), IdentityCode.TYPE_LOGIN) != null) {
            return ResponseModel.buildOk();
        } else {
            return ResponseModel.buildServiceError();
        }
    }

    private ResponseModel<AccountRspModel> getSms(LoginIcModel model) {
        User user = UserFactory.findByPhone(model.getPhone());
        if (user == null) {
            //手机号没有绑定
            return ResponseModel.buildPhoneNotBindError();
        }

        if (!Strings.isNullOrEmpty(model.getPushId()) && model.getPushId().equals(user.getPushId())) {
            user = UserFactory.login(user);
            AccountRspModel rspModel = new AccountRspModel(user);
            return ResponseModel.buildOk(rspModel);
        }

        //发送前检查是否发送过于频繁
        if (!ICodeUtil.checkFrequent(model.getPhone())) {
            return ResponseModel.buildServiceBusyError();
        }

        //发送验证码
        if (Notify3rdFactory.sendIcSms(model.getPhone(), IdentityCode.TYPE_LOGIN) != null) {
            return ResponseModel.buildOk();
        } else {
            return ResponseModel.buildServiceError();
        }
    }

    //手机号注册
    @POST
    @Path("/register")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> register(RegisterModel model) {
        if (!RegisterModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        User user = UserFactory.findByPhone(model.getPhone().trim());
        String account;
        if (user != null) {
            return ResponseModel.buildHavePhoneError();
        } else {
            //手机号还没有注册,手机号传入形式+86 ***********.
            String[] tmp = model.getPhone().split(" ");
            if (tmp.length != 2) {
                //参数异常,手机号的格式不对
                return ResponseModel.buildParameterError();
            }
            account = tmp[1];
            user = UserFactory.findByAccount(account);
            if (user != null) {
                //手机号已经被账号占用
                account = account + "@scgl";
            }
        }


        //昵称是否已经存在
        user = UserFactory.findByName(model.getName().trim());
        if (user != null) {
            return ResponseModel.buildHaveNameError();
        }

        //账号是否已经存在,出现这个情况,一般可能是服务器出了注册异常
        user = UserFactory.findByAccount(account);
        if (user != null) {
            return ResponseModel.buildServiceError();
        }

        IdentityCode identityCode = Notify3rdFactory.findByIdentify(model.getPhone());
        if (!ICodeUtil.checkIdentifyCode(identityCode, model.getCode())) {
            return ResponseModel.buildIdentifyCodeError();
        }

        user = UserFactory.register(account, model.getPassword(), model.getName(), model.getPhone());
        if (user != null) {
            AdminFactory.createOriginAnth(user, 1);
            AdminFactory.createKeepsAnth(user, 1);
            AdminFactory.sendWelcome(user);
        }

        return bindAndReturn(user, model.getPushId(), ResponseModel.buildRegisterError());
    }

    //手机号注册验证
    @POST
    @Path("/verify")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> verify(RegisterModel model) {
        if (!RegisterModel.checkForVerify(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        //手机号是否已经绑定
        User user = UserFactory.findByPhone(model.getPhone().trim());
        if (user != null) {
            return ResponseModel.buildHavePhoneError();
        }

        //昵称是否已经存在
        user = UserFactory.findByName(model.getName().trim());
        if (user != null) {
            return ResponseModel.buildHaveNameError();
        }

        //发送前检查是否发送过于频繁
        if (!ICodeUtil.checkFrequent(model.getPhone())) {
            return ResponseModel.buildServiceBusyError();
        }

        //发送验证码
        if (Notify3rdFactory.sendIcSms(model.getPhone(), IdentityCode.TYPE_REGISTER) != null) {
            return ResponseModel.buildOk();
        } else {
            return ResponseModel.buildServiceError();
        }

    }

    @POST
    @Path("/register3rd")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> register(Register3rdModel model) {
        if (!Register3rdModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        //是否已经被绑定
        String account;
        String qqId = null;
        String sinaId = null;
        if (model.getType() == Register3rdModel.TYPE_QQ) {
            User user = UserFactory.findByQQ(model.getOpenId());
            if (user != null) {
                //QQ已经被绑定
                return ResponseModel.buildHaveQQError();
            } else {
                qqId = model.getOpenId();
                account = "QQ" + qqId;
            }
        } else if (model.getType() == Register3rdModel.TYPE_SINA) {
//            User user = UserFactory.findBySina(model.getOpenId());
//            if (user != null) {
//                //QQ已经被绑定
//                return ResponseModel.buildHaveQQError();
//            } else {
//                sinaId = model.getOpenId();
//                account = "Sina" + sinaId;
//            }
            //不再支持sina注册
            return ResponseModel.buildRegisterError();
        } else {
            return ResponseModel.buildRegisterError();
        }

        //昵称是否已经存在
        User user = UserFactory.findByName(model.getName().trim());
        if (user != null) {
            return ResponseModel.buildHaveNameError();
        }

        user = UserFactory.register3rd(account, model.getPassword(), model.getName(), qqId, null);

        if (user != null) {
            AdminFactory.createOriginAnth(user, 1);
            AdminFactory.createKeepsAnth(user, 1);
            AdminFactory.sendWelcome(user);
        }
        return bindAndReturn(user, model.getPushId(), ResponseModel.buildRegisterError());
    }

    private ResponseModel<AccountRspModel> bindAndReturn(User user, String pushId,
                                                         ResponseModel<AccountRspModel> errResponse) {
        if (user != null) {
            //账号是否已经被锁定
            if (0 != user.getLocked()) {
                return ResponseModel.buildLockedError();
            }
            //如果携带了PushId
            if (!Strings.isNullOrEmpty(pushId)) {
                return bind(user, pushId);
            }

            AccountRspModel rspModel = new AccountRspModel(user);
            return ResponseModel.buildOk(rspModel);
        } else {
            return errResponse;
        }
    }

    /**
     * @deprecated 2018-4-19 放在URL里容易出现异常
     */
    @PUT
    @Path("/bind/{pushId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> bind(@HeaderParam("token") String token,
                                               @PathParam("pushId") String pushId) {
        if (Strings.isNullOrEmpty(token) || Strings.isNullOrEmpty(pushId)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        //获取个人信息
        User user = UserFactory.findByToken(token);

        if (user == null) {
            return ResponseModel.buildServiceError();
        }

        return bind(user, pushId);
    }

    @PUT
    @Path("/bind")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> bind(@HeaderParam("token") String token,
                                               StringParamModel param) {
        if (Strings.isNullOrEmpty(token) || Strings.isNullOrEmpty(param.getParam())) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        //获取个人信息
        User user = UserFactory.findByToken(token);

        if (user == null) {
            return ResponseModel.buildServiceError();
        }

        return bind(user, param.getParam());
    }

    /**
     * 绑定的操作
     *
     * @param self   自己
     * @param pushId push id
     * @return User
     */
    private ResponseModel<AccountRspModel> bind(User self, String pushId) {
        User user = UserFactory.bindPushId(self, pushId);
        if (user == null) {
            return ResponseModel.buildServiceError();
        }

        AccountRspModel rspModel = new AccountRspModel(user, true);
        return ResponseModel.buildOk(rspModel);
    }


    //历史账户自动登录
    @POST
    @Path("/login4O")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountRspModel> login4O(Login4OModel model) {

        if (Strings.isNullOrEmpty(model.getLoginId())) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        String account, password;
        try {
            String accPwd = VerUtil.decryptOldId(model.getLoginId());
            String[] tmp = accPwd.split("\\|");
            account = tmp[0];
            password = tmp[1];
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseModel.buildParameterError();
        }

        User user = UserFactory.login(account, password, true);
        return bindAndReturn(user, model.getPushId(), ResponseModel.buildLoginError());
    }

    /**
     * 程序启动时调用一次
     */
    @PUT
    @Path("/launch")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<UserCard> launch(LaunchModel launchModel) {
        //手机的设备标识不能为空
        if (Strings.isNullOrEmpty(launchModel.getId())) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        //获取个人信息
        User user = null;
        if (!Strings.isNullOrEmpty(launchModel.getToken())) {
            user = UserFactory.findByToken(launchModel.getToken());
        }

        //记录启动信息
        Launch launch = UserFactory.findLaunch(launchModel.getId());
        if (launch == null) {
            launch = new Launch(user, launchModel);
        } else {
            launch.update(user, launchModel);
        }
        UserFactory.saveLaunch(launch);

        if (user != null) {
            return ResponseModel.buildOk(new UserCard(user));
        } else {
            return ResponseModel.buildOk();
        }
    }
}

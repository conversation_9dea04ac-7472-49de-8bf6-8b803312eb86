package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.api.weibo.CommentCreateModel;
import shanks.scgl.bean.card.CommentCard;
import shanks.scgl.bean.db.*;
import shanks.scgl.factory.CommentFactory;
import shanks.scgl.factory.PushFactory;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.factory.WeiboFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Path("/comment")
public class CommentService extends BaseService {
    @GET
    @Path("/{wid}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<CommentCard>> listOfWeibo(@PathParam("wid") int wid, @PathParam("min") long min,
                                                        @PathParam("max") long max) {

        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Comment> comments = CommentFactory.list(wid, minTime, maxTime);
        //因为@的基数会很大,不断增长,不像关注可以限定人数,而且关注是可以修改的,但是评论不能修改.
        //所以采用客户端进行处理的方式,此处直接定义为false
        List<CommentCard> cards = comments.stream()
                                          .map(comment -> new CommentCard(comment, false))
                                          .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/toMe/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<CommentCard>> listToMe(@PathParam("min") long min, @PathParam("max") long max) {

        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Comment> comments = CommentFactory.listToMe(getSelf(), minTime, maxTime);
        List<CommentCard> cards = comments.stream()
                                          .map(comment -> new CommentCard(comment, false))
                                          .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/meTo/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<CommentCard>> listMeTo(@PathParam("min") long min, @PathParam("max") long max) {

        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Comment> comments = CommentFactory.listMeTo(getSelf(), minTime, maxTime);
        List<CommentCard> cards = comments.stream()
                                          .map(comment -> new CommentCard(comment, false))
                                          .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/atMe/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<CommentCard>> listAtMe(@PathParam("min") long min, @PathParam("max") long max) {

        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Comment> comments = CommentFactory.listAtMe(getSelf(), minTime, maxTime);
        List<CommentCard> cards = comments.stream()
                                          .map(comment -> new CommentCard(comment, true))
                                          .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<CommentCard> comment(@HeaderParam("X-Real-IP") String realIP,
                                              CommentCreateModel model) {
        if (!CommentCreateModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        Weibo weibo = WeiboFactory.findById(model.getWid());
        if (weibo == null) {
            //要评论的微博不存在
            return ResponseModel.buildNotFoundWeiboError(null);
        }

        //是否已经将我拉黑
        Blacklist blacklist = UserFactory.getUserBlack(weibo.getUid(), getSelf().getId());
        if (blacklist != null) {
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_IS_BLACK);
        }

        if (model.getParent() != 0) {
            Comment comment = CommentFactory.findById(model.getParent());
            if (comment == null) {
                //参数异常,有父节点,但是父节点却找不到
                return ResponseModel.buildParameterError();
            }
        }

        Comment comment = CommentFactory.create(getSelf(), weibo, model, realIP);

        if (comment == null) {
            return ResponseModel.buildCreateError(ResponseModel.ERROR_CREATE_COMMENT);
        }

        //检测是否有@
        handleAt(model.getContent(), weibo, comment);

        //我自己评论的,@me为false
        CommentCard card = new CommentCard(comment, false);

        //自己评论自己,不用发提醒
        if (getSelf().getId() != weibo.getUid()) {
            //给被评论的用户发送一条消息,提醒有新的评论.评论的时候不知道是否at,可以复用
            User receiver = UserFactory.findById(weibo.getUid());
            PushFactory.pushNewComment(getSelf(), receiver, card);
        }

        return ResponseModel.buildOk(card);
    }

    @DELETE
    @Path("/{cid}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<CommentCard> delete(@PathParam("cid") int cid) {
        if (cid == 0) {
            //参数异常
            return ResponseModel.buildParameterError();
        }
        Comment comment = CommentFactory.findById(cid);
        if (comment == null) {
            return ResponseModel.buildParameterError();
        }
        User self = getSelf();
        //必须是我写的评论,或者这个微博是我发的
        if (comment.getUid() != self.getId() && comment.getWuid() != self.getId()) {
            return ResponseModel.buildNoPermissionError();
        }
        comment = CommentFactory.delete(comment);
        if (comment == null) {
            return ResponseModel.buildServiceError();
        }

        return ResponseModel.buildOk(new CommentCard(comment, false));
    }

    private void handleAt(String commentContent, Weibo weibo, Comment comment) {
        Pattern pattern = Pattern.compile("@(\\S+?)\\s");
        Matcher matcher = pattern.matcher(commentContent);

        CommentCard card = new CommentCard(comment, true);
        User self = getSelf();
        while (matcher.find()) {
            String name = matcher.group(1);
            if (Strings.isNullOrEmpty(name)) {
                continue;
            }

            User user = UserFactory.findByName(name);
            //不用给自己发@
            if (user == null || user.getId() == self.getId()) {
                continue;
            }

            Atme atme = CommentFactory.createAtme(user, weibo, comment);
            if (atme != null) {
                // 给被@的用户发送一条消息,提醒被@
                PushFactory.pushNewAtme(self, user, card);
            }

        }
    }
}

package shanks.scgl.service.v1;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.VisitCard;
import shanks.scgl.bean.db.User;
import shanks.scgl.bean.db.UserInfo;
import shanks.scgl.bean.db.Visitor;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.factory.VisitFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * UserService和UserFactory里的代码太多了,单独拿出来
 */
@Path("/visit")
public class VisitService extends BaseService {
    @PUT
    @Path("/{visited}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<VisitCard> visit(@PathParam("visited") int visitedId) {
        if (visitedId == 0) {
            return ResponseModel.buildParameterError();
        }

        User visited = UserFactory.findById(visitedId);
        if (visited == null) {
            return ResponseModel.buildParameterError();
        }
        User visitor = getSelf();
        if (visited.getId() == visitor.getId()) {
            return ResponseModel.buildParameterError();
        }

        Visitor recorder = VisitFactory.find(visited, getSelf());

        recorder = VisitFactory.visit(recorder, visited, visitor);

        VisitCard card = new VisitCard(recorder);

        return ResponseModel.buildOk(card);
    }

    //被访问者的所有访客
    @GET
    @Path("/{visited}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<VisitCard>> visitors(@PathParam("visited") int visitedId,
                                                   @PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        if (visitedId == 0) {
            return ResponseModel.buildParameterError();
        }

        User visited = UserFactory.findById(visitedId);
        if (visited == null) {
            return ResponseModel.buildParameterError();
        }

        List<Visitor> visitors = VisitFactory.visitors(visited,minTime,maxTime);

        List<VisitCard> cards = visitors.stream()
                                        .map(VisitCard::new)
                                        .collect(Collectors.toList());

        return ResponseModel.buildOk(cards);
    }

    //我的拜访记录
    @GET
    @Path("/iVisit/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<VisitCard>> iVisit( @PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Visitor> visitors = VisitFactory.visits(getSelf(),minTime,maxTime);

        List<VisitCard> cards = visitors.stream()
                                        .map(VisitCard::new)
                                        .collect(Collectors.toList());

        return ResponseModel.buildOk(cards);
    }
}

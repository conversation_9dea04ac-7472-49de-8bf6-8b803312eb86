package shanks.scgl.service.v1;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.OSSTCard;
import shanks.scgl.bean.db.User;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.OSSUtil;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

@Path("/sec")
public class SecurityService extends BaseService {
    @GET
    @Path("/osst")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<OSSTCard> ossToken() {
        User self = getSelf();

        String roleId="SCGL"+self.getId();
        OSSTCard token = OSSUtil.getToken(roleId);
        if (token == null) {
            return ResponseModel.buildOssPermGrantError();
        }

        return ResponseModel.buildOk(token);
    }
}

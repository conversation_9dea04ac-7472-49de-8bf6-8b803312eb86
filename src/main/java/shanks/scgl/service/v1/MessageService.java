package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.api.message.MessageCreateModel;
import shanks.scgl.bean.card.MessageCard;
import shanks.scgl.bean.db.Blacklist;
import shanks.scgl.bean.db.Message;
import shanks.scgl.bean.db.User;
import shanks.scgl.factory.MessageFactory;
import shanks.scgl.factory.PushFactory;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 消息发送的入口
 */
@Path("/msg")
public class MessageService extends BaseService {
    /**
     * 发送一条消息到服务器
     */
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<MessageCard> pushMessage(MessageCreateModel model) {
        if (!MessageCreateModel.check(model)) {
            return ResponseModel.buildParameterError();
        }


        User self = getSelf();
        //查询数据库中是否已经存在
        Message message = MessageFactory.findById(model.getId());
        if (message != null) {
            //正常返回
            return ResponseModel.buildOk(new MessageCard(message));
        }

        return pushToUser(self, model);
    }

    //发送到人
    private ResponseModel<MessageCard> pushToUser(User sender, MessageCreateModel model) {
        User receiver = UserFactory.findById(model.getReceiverId());
        if (receiver == null) {
            //没有找到接收者
            return ResponseModel.buildNotFoundUserError("can not find receiver");
        }

        //接收者是否已经将发送者拉入黑名单
        Blacklist blacklist = UserFactory.getUserBlack(receiver, sender);
        if (blacklist != null) {
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_IS_BLACK);
        }

        if (receiver.getId() == sender.getId()) {
            //发送者和接收者是同一个人,就返回创建消息失败
            return ResponseModel.buildCreateError(ResponseModel.ERROR_CREATE_MESSAGE);
        }
        //存储数据库
        Message message = MessageFactory.add(sender, receiver, model);

        return buildAndPushResponse(sender, message);
    }

    //推送并构建一个返回信息
    private ResponseModel<MessageCard> buildAndPushResponse(User sender, Message message) {
        if (message == null) {
            //存储数据库失败
            return ResponseModel.buildCreateError(ResponseModel.ERROR_CREATE_MESSAGE);
        }

        //进行推送
        PushFactory.pushNewMessage(sender, message);

        //返回
        return ResponseModel.buildOk(new MessageCard(message));
    }

    /**
     * @deprecated 已经弃用, 不再向服务器轮训, 获取最新的消息
     */
    @GET
    @Path("/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<MessageCard>> messages(@PathParam("min") long min,
                                                     @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Message> messages = MessageFactory.list(getSelf(), minTime, maxTime);

        List<MessageCard> cards = messages.stream().map(MessageCard::new).collect(Collectors.toList());

        return ResponseModel.buildOk(cards);
    }

    /**
     * 获取与指定用户之间的往来消息
     */
    @GET
    @Path("/{uid}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<MessageCard>> messages(@PathParam("uid") int uid, @PathParam("min") long min,
                                                     @PathParam("max") long max) {
        User other = UserFactory.findById(uid);
        if (other == null) {
            return ResponseModel.buildParameterError();
        }

        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Message> messages = MessageFactory.list(getSelf(), other, minTime, maxTime);

        List<MessageCard> cards = messages.stream().map(MessageCard::new).collect(Collectors.toList());

        return ResponseModel.buildOk(cards);
    }

    /**
     * 删除指定消息
     */
    @DELETE
    @Path("/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel deleteMsg(@PathParam("id") String id) {
        if (Strings.isNullOrEmpty(id)) {
            return ResponseModel.buildParameterError();
        }
        Message message = MessageFactory.findById(id);
        if (message == null) {
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        //发送者或者接收者是自己 TODO 接收者是自己的也删了，逻辑是否有问题？
        if (message.getSenderId() != self.getId() && message.getReceiverId() != self.getId()) {
            return ResponseModel.buildNoPermissionError();
        }

        MessageFactory.delete(message);

        return ResponseModel.buildOk();
    }
}

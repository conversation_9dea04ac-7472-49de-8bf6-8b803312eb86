package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.api.weibo.WeiboCreateModel;
import shanks.scgl.bean.card.WeiboCard;
import shanks.scgl.bean.db.*;
import shanks.scgl.factory.AnthFactory;
import shanks.scgl.factory.PoemFactory;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.factory.WeiboFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Path("/weibo")
public class WeiboService extends BaseService {
    @GET
    @Path("/circle/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboCard>> circle(@PathParam("min") long min, @PathParam("max") long max) {
        User self = getSelf();
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        //最多搜索半年
        LocalDateTime halfYear = LocalDateTime.now().minusMonths(6);
        if (minTime.isBefore(halfYear)) {
            minTime = halfYear;
        }

        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        //关注2000人,用时30ms左右
        List<Weibo> weibos = WeiboFactory.circle(self, minTime, maxTime);

        //10ms以内
        WeiboFactory.addViewCount(weibos);

        List<WeiboCard> cards = weibos.stream()
                                      .map(weibo -> new WeiboCard(weibo, true))
                                      .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/post/{uid}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboCard>> post(@PathParam("uid") int uid, @PathParam("min") long min,
                                               @PathParam("max") long max) {
        User user = UserFactory.findById(uid);

        if (user == null) {
            return ResponseModel.buildNotFoundUserError(null);
        }
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Weibo> weibos = WeiboFactory.list(user, minTime, maxTime);

        WeiboFactory.addViewCount(weibos);
        UserFollow follow = UserFactory.getUserFollow(getSelf(), user);
        boolean isRelated = follow != null || uid == getSelf().getId();
        List<WeiboCard> cards = weibos.stream()
                                      .map(weibo -> new WeiboCard(weibo, isRelated))
                                      .collect(Collectors.toList());

        return ResponseModel.buildOk(cards);
    }


    @Deprecated
    @GET
    @Path("/piazza/{type}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboCard>> piazza(@PathParam("type") int type, @PathParam("min") long min,
                                                 @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Weibo> weibos = WeiboFactory.list(type, minTime, maxTime);
        //主要耗时的在这里,以后再做优化
        int myId = getSelf().getId();
        List<Integer> followIds = UserFactory.getFollowIds(myId);
        //把自己加上
        followIds.add(myId);
        //下面这个耗时很低
        HashSet<Integer> followsSet = new HashSet<>(followIds);
        List<WeiboCard> cards = new ArrayList<>();
        boolean isRelated;
        for (Weibo weibo : weibos) {
            isRelated = followsSet.contains(weibo.getUid());
            cards.add(new WeiboCard(weibo, isRelated));
        }

        WeiboFactory.addViewCount(weibos);
        return ResponseModel.buildOk(cards);
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<WeiboCard> publish(@HeaderParam("X-Real-IP") String realIP,
                                            WeiboCreateModel model) {
        if (!WeiboCreateModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        Weibo weibo = null;
        //先用poemId找
        if (!Strings.isNullOrEmpty(model.getPoemId())) {
            weibo = WeiboFactory.findByPoemId(model.getPoemId());
        } else if (model.getId() != 0) {
            weibo = WeiboFactory.findById(model.getId());
            if (weibo == null) {
                return ResponseModel.buildParameterError();
            }
        }

        Anthology anthology = AnthFactory.findById(model.getAnthId());
        if (anthology == null) {
            return ResponseModel.buildParameterError();
        }

        if (weibo == null) {
            //新建
            //满了
            if (AnthFactory.getAnthSize(anthology) >= Constant.MAX_ANTH_SIZE) {
                return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_ANTH_IS_FULL);
            }
            weibo = WeiboFactory.create(getSelf(), anthology, model, realIP);
        } else {
            if (getSelf().getId() != weibo.getUid()) {
                //不是自己的作品
                return ResponseModel.buildNoPermissionError();
            }
            //更新
            weibo = WeiboFactory.update(weibo, model, anthology, realIP);
        }

        if (weibo == null) {
            //创建失败
            return ResponseModel.buildCreateError(ResponseModel.ERROR_CREATE_WEIBO);
        }

        WeiboCard card = new WeiboCard(weibo, true);

        return ResponseModel.buildOk(card);
    }

    @DELETE
    @Path("{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<WeiboCard> delete(@PathParam("id") int id) {
        Weibo weibo = WeiboFactory.findById(id);
        if (weibo == null) {
            return ResponseModel.buildNotFoundWeiboError(null);
        }
        User self = getSelf();
        if (weibo.getUid() != self.getId()) {
            return ResponseModel.buildNoPermissionError();
        }
        //目前是硬删除,以后可以改为软删除
        weibo = WeiboFactory.delete(weibo, self);
        WeiboCard card = new WeiboCard(weibo, true, true);

        return ResponseModel.buildOk(card);
    }

    /**
     * 拉取指定微博的信息
     */
    @GET
    @Path("{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<WeiboCard> getWeibo(@PathParam("id") int id) {
        Weibo weibo = WeiboFactory.findById(id);
        if (weibo == null) {
            return ResponseModel.buildNotFoundWeiboError(null);
        }
        WeiboFactory.addViewCount(weibo);

        User self = getSelf();
        UserFollow follow = UserFactory.getUserFollow(self.getId(), weibo.getUid());
        boolean isRelated = follow != null || weibo.getUid() == self.getId();
        return ResponseModel.buildOk(new WeiboCard(weibo, isRelated));
    }

    @Deprecated
    @PUT
    @Path("/bind/{weiboId}/{poemId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<WeiboCard> bind(@PathParam("weiboId") int weiboId, @PathParam("poemId") String poemId) {
        if (Strings.isNullOrEmpty(poemId)) {
            return ResponseModel.buildParameterError();
        }
        Weibo weibo = WeiboFactory.findById(weiboId);
        if (weibo == null) {
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        if (self.getId() != weibo.getUid()) {
            //不是自己的,没有权限绑定
            return ResponseModel.buildNoPermissionError();
        }

        Poem poem = PoemFactory.findById(poemId);
        if (poem != null && poem.getUid() != weibo.getUid()) {
            //不是自己的,没有权限绑定
            return ResponseModel.buildNoPermissionError();
        }

        if (WeiboFactory.findByPoemId(poemId) != null) {
            //已经被其他的绑定了
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_POEM_IS_BIND);
        }

        WeiboFactory.bind(weibo, poemId);

        return ResponseModel.buildOk(new WeiboCard(weibo, true));
    }

    @Deprecated
    @GET
    @Path("/search/{key:(.*)?}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboCard>> search(@DefaultValue("") @PathParam("key") String key) {

        List<Weibo> weibos = WeiboFactory.search(key);
        //主要耗时的在这里,以后再做优化
        int myId = getSelf().getId();
        List<Integer> followIds = UserFactory.getFollowIds(myId);
        //把自己加上
        followIds.add(myId);
        //下面这个耗时很低
        HashSet<Integer> followsSet = new HashSet<>(followIds);
        List<WeiboCard> cards = new ArrayList<>();
        boolean isRelated;
        for (Weibo weibo : weibos) {
            isRelated = followsSet.contains(weibo.getUid());
            cards.add(new WeiboCard(weibo, isRelated));
        }
        //时间差不多
//        List<WeiboCard> cards = weibos.stream()
//                                    .map(weibo -> new WeiboCard(weibo,false))
//                                    .collect(Collectors.toList());

        //返回
        return ResponseModel.buildOk(cards);
    }

    @Deprecated
    @GET
    @Path("/search/{self}/{key:(.*)?}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboCard>> searchSelf(@DefaultValue("") @PathParam("key") String key,
                                                     @PathParam("self") boolean isSelf) {

        List<Weibo> weibos;
        User self = getSelf();
        if (isSelf) {
            weibos = WeiboFactory.search(self, key);
        } else {
            weibos = WeiboFactory.search(key);
        }
        //主要耗时的在这里,以后再做优化
        int myId = self.getId();
        List<Integer> followIds = UserFactory.getFollowIds(myId);
        //把自己加上
        followIds.add(myId);
        //下面这个耗时很低
        HashSet<Integer> followsSet = new HashSet<>(followIds);
        List<WeiboCard> cards = new ArrayList<>();
        boolean isRelated;
        for (Weibo weibo : weibos) {
            isRelated = followsSet.contains(weibo.getUid());
            cards.add(new WeiboCard(weibo, isRelated));
        }
        //时间差不多
//        List<WeiboCard> cards = weibos.stream()
//                                    .map(weibo -> new WeiboCard(weibo,false))
//                                    .collect(Collectors.toList());

        //返回
        return ResponseModel.buildOk(cards);
    }
}

package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.api.rule.MarkCreateModel;
import shanks.scgl.bean.api.rule.RuleCreateModel;
import shanks.scgl.bean.card.MarkCard;
import shanks.scgl.bean.card.RuleCard;
import shanks.scgl.bean.db.Mark;
import shanks.scgl.bean.db.Rule;
import shanks.scgl.bean.db.User;
import shanks.scgl.factory.PushFactory;
import shanks.scgl.factory.RuleFactory;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Path("/rule")
public class RuleService extends BaseService {
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<RuleCard> publish(RuleCreateModel model) {
        if (!RuleCreateModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        Rule rule = RuleFactory.findById(model.getId());
        if (rule == null) {
            //服务器没有保存,需要保存
            rule = new Rule(model, self);
        } else if (rule.getUid() != self.getId()) {
            //不是Owner
            return ResponseModel.buildNoPermissionError();
        } else {
            //更新
            rule.update(model);
        }

        rule = RuleFactory.saveOrUpdate(rule);
        RuleCard card = new RuleCard(rule);

        return ResponseModel.buildOk(card);
    }

    @GET
    @Path("/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<RuleCard>> rules(@PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
        List<Rule> rules = RuleFactory.list(minTime, maxTime);

        List<RuleCard> cards = rules.stream()
                                    .map(RuleCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/{type}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<RuleCard>> rules(@PathParam("type") String type, @PathParam("min") long min, @PathParam
            ("max") long max) {
        if (Strings.isNullOrEmpty(type)) {
            return ResponseModel.buildParameterError();
        }
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
        List<Rule> rules = RuleFactory.list(type, minTime, maxTime);

        List<RuleCard> cards = rules.stream()
                                    .map(RuleCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/search/{key:(.*)?}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<RuleCard>> search(@DefaultValue("") @PathParam("key") String key) {

        List<Rule> rules = RuleFactory.search(key);
        List<RuleCard> cards = rules.stream()
                                    .map(RuleCard::new)
                                    .collect(Collectors.toList());
        //返回
        return ResponseModel.buildOk(cards);
    }


    @PUT
    @Path("/mark")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<MarkCard> mark(MarkCreateModel model) {
        if (!MarkCreateModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        Rule rule = RuleFactory.findById(model.getRid());
        if (rule == null) {
            return ResponseModel.buildParameterError();
        }

        Mark mark = RuleFactory.findMark(rule, model.getVersion(), self);
        if (mark != null) {
            //同一个版本只能评分一次
            return ResponseModel.buildAlreadyMarkError();
        }

        mark = RuleFactory.mark(rule, self, model);
        if (mark == null) {
            return ResponseModel.buildServiceError();
        }
        RuleFactory.reCalMark(rule);

        MarkCard card = new MarkCard(mark);
        //给被评分的人发一条通知消息
        User receiver = UserFactory.findById(rule.getUid());
        PushFactory.pushNewMark(getSelf(), receiver, card);

        return ResponseModel.buildOk(card);
    }

    @GET
    @Path("/{rid}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<RuleCard> getRule(@PathParam("rid") String rid) {
        if (Strings.isNullOrEmpty(rid)) {
            return ResponseModel.buildParameterError();
        }

        Rule rule = RuleFactory.findById(rid);
        if (rule == null) {
            return ResponseModel.buildParameterError();
        }

        return ResponseModel.buildOk(new RuleCard(rule));
    }

    @GET
    @Path("/mark/{ruleId}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<MarkCard>> marks(@PathParam("ruleId") String ruleId, @PathParam("min") long min,
                                               @PathParam("max") long max) {
        if (Strings.isNullOrEmpty(ruleId)) {
            return ResponseModel.buildParameterError();
        }
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
        List<Mark> marks = RuleFactory.marks(ruleId, minTime, maxTime);

        List<MarkCard> cards = marks.stream()
                                    .map(MarkCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @POST
    @Path("/download/{ruleId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel download(@PathParam("ruleId") String ruleId) {
        if (Strings.isNullOrEmpty(ruleId)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        Rule rule = RuleFactory.findById(ruleId);
        if (rule == null) {
            return ResponseModel.buildParameterError();
        }

        //只更新一下下载次数,不记录详细信息
        RuleFactory.recorderDownload(rule);

        return ResponseModel.buildOk();
    }

    /**
     * 获取指定类型的所有格律名称
     *
     * @param type 类型
     * @return 名称列表
     */
    @GET
    @Path("/pub/names/{type}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<String>> names(@PathParam("type") String type) {
        if (Strings.isNullOrEmpty(type)) {
            return ResponseModel.buildParameterError();
        }

        List<String> names = RuleFactory.names(type);
        return ResponseModel.buildOk(names);
    }

    /**
     * 获取指定名称的格律
     *
     * @param name 格律名称
     * @return 格律列表
     */
    @GET
    @Path("/pub/rules/{name}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<RuleCard>> rules(@PathParam("name") String name) {
        if (Strings.isNullOrEmpty(name)) {
            return ResponseModel.buildParameterError();
        }

        List<Rule> rules = RuleFactory.rules(name);

        List<RuleCard> cards = rules.stream()
                                    .map(RuleCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/pub/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<RuleCard>> pubRules(@PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
        List<Rule> rules = RuleFactory.list(minTime, maxTime);

        List<RuleCard> cards = rules.stream()
                                    .map(RuleCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    /**
     * 我上传的格律
     *
     * @param min 更新时间最小值
     * @param max 更新时间最大值
     * @return 我的格律列表
     */
    @GET
    @Path("/mine/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<RuleCard>> myRules(@PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
        List<Rule> rules = RuleFactory.list(getSelf(), minTime, maxTime);

        List<RuleCard> cards = rules.stream()
                                    .map(RuleCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/marks/me/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<MarkCard>> markMe(@PathParam("min") long min,
                                                @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
        List<Mark> marks = RuleFactory.markMe(getSelf(), minTime, maxTime);

        List<MarkCard> cards = marks.stream()
                                    .map(MarkCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/marks/my/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<MarkCard>> myMark(@PathParam("min") long min,
                                                @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
        List<Mark> marks = RuleFactory.myMark(getSelf(), minTime, maxTime);

        List<MarkCard> cards = marks.stream()
                                    .map(MarkCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @PUT
    @Path("/status/{ruleId}/{status}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel updateStatus(@PathParam("ruleId") String ruleId, @PathParam("status") int status) {
        //只能设置上线和下线
        if (status != Rule.STATUS_ON_LINE && status != Rule.STATUS_OFF_LINE) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        Rule rule = RuleFactory.findById(ruleId);
        if (rule == null) {
            return ResponseModel.buildParameterError();
        }

        if (rule.getUid() != getSelf().getId()) {
            //不是Owner
            return ResponseModel.buildNoPermissionError();
        }

        if (rule.getStatus() != Rule.STATUS_ON_LINE && rule.getStatus() != Rule.STATUS_OFF_LINE) {
            return ResponseModel.buildParameterError();
        }

        rule.setStatus(status);
        rule = RuleFactory.saveOrUpdate(rule);

        return ResponseModel.buildOk(new RuleCard(rule));
    }
}

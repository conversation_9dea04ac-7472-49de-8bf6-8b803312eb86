package shanks.scgl.service.v1;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.User;
import shanks.scgl.factory.SocialFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;

@Path("/social")
public class SocialService  extends BaseService {
    @GET
    @Path("/talent/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<UserCard>> talent(@PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        User self = getSelf();
        List<UserCard> cards = SocialFactory.talent(self, minTime, maxTime);
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/vips/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<UserCard>> vips(@PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        User self = getSelf();

        List<UserCard> cards = SocialFactory.vips(self, minTime, maxTime);
        return ResponseModel.buildOk(cards);
    }

//    @GET
//    @Path("/fresher/{min}/{max}")
//    @Consumes(MediaType.APPLICATION_JSON)
//    @Produces(MediaType.APPLICATION_JSON)
//    public ResponseModel<List<UserCard>> fresher(@PathParam("min") long min, @PathParam("max") long max) {
//        LocalDateTime minTime = TimeUtil.getMinTime(min);
//        LocalDateTime maxTime = TimeUtil.getMaxTime(max);
//
//        User self = getSelf();
//
//        List<UserCard> cards = SocialFactory.fresher(self, minTime, maxTime);
//        return ResponseModel.buildOk(cards);
//    }
}

package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.api.poem.PoemCreateModel;
import shanks.scgl.bean.card.PoemCard;
import shanks.scgl.bean.card.SummaryItem;
import shanks.scgl.bean.db.Poem;
import shanks.scgl.bean.db.User;
import shanks.scgl.factory.PoemFactory;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Path("/poem")
public class PoemService extends BaseService {
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<PoemCard> push(PoemCreateModel model) {
        if (!PoemCreateModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        Poem poem = PoemFactory.findById(model.getId());
        PoemCard card;
        if (poem == null) {
            //服务器没有保存,需要保存
            poem = new Poem(model, self);
            poem = PoemFactory.saveOrUpdate(poem);
            card = new PoemCard(poem, PoemCard.ACTION_UPLOAD);
        } else {
            if (poem.getUid() != self.getId()) {
                //不是自己的作品
                card = new PoemCard(poem, PoemCard.ACTION_NONE);
            } else {
                //这里只考虑了单端的情况，如果是多端的情况，需要考虑更新的时间戳。做微信小程序的时候，需要写新版接口。
                poem.update(model);
                poem = PoemFactory.saveOrUpdate(poem);
                //客户端的较新,告知客户端上传最新文件
                card = new PoemCard(poem, PoemCard.ACTION_UPLOAD);
            }
        }

        return ResponseModel.buildOk(card);
    }

    @GET
    @Path("/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<PoemCard>> poems(@PathParam("min") long min, @PathParam("max") long max) {
        User self = getSelf();
        if (!UserFactory.isVIP(self)) {
            // 判断是否是VIP
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_NOT_VIP);
        }

        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Poem> poems = PoemFactory.list(getSelf(), minTime, maxTime);
        List<PoemCard> cards = new ArrayList<>();
        for (Poem poem : poems) {
            cards.add(new PoemCard(poem, PoemCard.ACTION_DOWNLOAD));
        }
        return ResponseModel.buildOk(cards);
    }

    @DELETE
    @Path("{poemId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<PoemCard> delete(@PathParam("poemId") String poemId) {
        if (Strings.isNullOrEmpty(poemId)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        Poem poem = PoemFactory.findById(poemId);

        if (poem == null) {
            return ResponseModel.buildNotFoundError(ResponseModel.ERROR_NOT_FOUND_POEM);
        }

        if (poem.getUid() != self.getId()) {
            return ResponseModel.buildNoPermissionError();
        }

        poem = PoemFactory.delete(poem);

        return ResponseModel.buildOk(new PoemCard(poem, PoemCard.ACTION_NONE));
    }

    @DELETE
    @Path("/folder/{folderName}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel deleteFolder(@PathParam("folderName") String folderName) {
        if (Strings.isNullOrEmpty(folderName)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();

        PoemFactory.delete(self,folderName);
        return ResponseModel.buildOk();
    }

    //查看已经备份的数据情况
    @Deprecated
    @GET
    @Path("/summary")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<SummaryItem>> summary() {
        User self = getSelf();

        List<SummaryItem> summary = new ArrayList<>();
        List result = PoemFactory.summary(self);
        for (Object o : result) {
            Object[] item = (Object[]) o;
            if (item == null || item.length != 2) {
                continue;
            }
            summary.add(new SummaryItem((String) item[0], (long)item[1]));
        }
        return ResponseModel.buildOk(summary);
    }
}

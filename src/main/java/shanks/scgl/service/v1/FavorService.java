package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.FavorCard;
import shanks.scgl.bean.db.*;
import shanks.scgl.factory.*;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Path("/favor")
public class FavorService extends BaseService {

    /**
     * 获取指定微博的favor合集
     */
    @GET
    @Path("/{wid}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<FavorCard>> favors(@PathParam("wid") int wid, @PathParam("min") long min, @PathParam
            ("max") long max) {
        if (wid == 0) {
            return ResponseModel.buildParameterError();
        }
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<FavorCard> allCards = new ArrayList<>();
        List<Keep> keeps = FavorFactory.keeps(wid, minTime, maxTime);
        List<FavorCard> keepCards = keeps.stream()
                                         .map(keep -> new FavorCard(keep, false))
                                         .collect(Collectors.toList());

        List<Share> shares = FavorFactory.shares(wid, minTime, maxTime);
        List<FavorCard> shareCards = shares.stream()
                                           .map(share -> new FavorCard(share, false))
                                           .collect(Collectors.toList());

        List<Favor> zans = FavorFactory.zans(wid, minTime, maxTime);
        List<FavorCard> zanCards = zans.stream()
                                       .map(zan -> new FavorCard(zan, false))
                                       .collect(Collectors.toList());

        allCards.addAll(keepCards);
        allCards.addAll(shareCards);
        allCards.addAll(zanCards);
        return ResponseModel.buildOk(allCards);
    }

    @PUT
    @Path("/keep/{weiboId}/{anthId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<FavorCard> keep(@PathParam("weiboId") int weiboId, @PathParam("anthId") String anthId) {
        //收藏的时候,需要制定归入的收藏集
        if (weiboId == 0 || Strings.isNullOrEmpty(anthId)) {
            return ResponseModel.buildParameterError();
        }

        Weibo weibo = WeiboFactory.findById(weiboId);
        if (weibo == null) {
            return ResponseModel.buildParameterError();
        }

        Anthology anthology = AnthFactory.findById(anthId);
        if (anthology == null) {
            return ResponseModel.buildParameterError();
        }
        //满了
        if (AnthFactory.getAnthSize(anthology) >= Constant.MAX_ANTH_SIZE) {
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_ANTH_IS_FULL);
        }

        User self = getSelf();
        Keep keep = FavorFactory.keep(weibo, self, anthology);
        if (keep == null) {
            return ResponseModel.buildServiceError();
        }

        return ResponseModel.buildOk(new FavorCard(keep, false));
    }

    @DELETE
    @Path("/keep/{kid}/{weiboId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<FavorCard> unKeep(@PathParam("kid") int kid, @PathParam("weiboId") int weiboId) {
        User self = getSelf();
        Weibo weibo = WeiboFactory.findById(weiboId);
        if (weibo == null) {
            return ResponseModel.buildParameterError();
        }

        Keep keep = FavorFactory.getKeep(kid);
        //没有记录,可能是网络的问题,客户端和服务器端数据不一致
        if (keep == null) {
            //构造一个并不存在的数据,把客户端数据状态更新掉(因为不想在客户端处理数据逻辑)
            FavorCard card = new FavorCard(weibo, self, FavorCard.TYPE_KEEP, true, kid);
            return ResponseModel.buildOk(card);
        }

        keep = FavorFactory.unKeep(weibo, keep);
        //出现错误
        if (keep == null) {
            return ResponseModel.buildServiceError();
        }

        return ResponseModel.buildOk(new FavorCard(keep, true));
    }


    @PUT
    @Path("/zan/{weiboId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<FavorCard> zan(@PathParam("weiboId") int weiboId) {
        Weibo weibo = WeiboFactory.findById(weiboId);
        if (weibo == null) {
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        if (self.loadInfo().getPoint() <= 0) {
            return ResponseModel.buildNoSufficientPointError();
        }

        Favor zan = FavorFactory.zan(weibo, self);
        if (zan == null) {
            return ResponseModel.buildServiceError();
        }

        FavorCard card = new FavorCard(zan, false);

        //给被点赞的用户发一条消息
        User receiver = UserFactory.findById(weibo.getUid());
        PushFactory.pushNewZan(self,receiver,card);

        return ResponseModel.buildOk(card);
    }

    @DELETE
    @Path("/zan/{zid}/{weiboId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<FavorCard> unZan(@PathParam("zid") int zid, @PathParam("weiboId") int weiboId) {
        User self = getSelf();
        Weibo weibo = WeiboFactory.findById(weiboId);
        if (weibo == null) {
            return ResponseModel.buildParameterError();
        }
        Favor zan = FavorFactory.getZan(zid);
        //没有记录,可能是网络的问题,客户端和服务器端数据不一致
        if (zan == null) {
            //构造一个并不存在的数据,把客户端数据状态更新掉(因为不想在客户端处理数据逻辑)
            FavorCard card = new FavorCard(weibo, self, FavorCard.TYPE_ZAN, true, zid);
            return ResponseModel.buildOk(card);
        }

        zan = FavorFactory.unZan(weibo, zan);
        //出现错误
        if (zan == null) {
            return ResponseModel.buildServiceError();
        }

        return ResponseModel.buildOk(new FavorCard(zan, true));
    }

    @POST
    @Path("/share/{weiboId}/{type}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<FavorCard> share(@PathParam("weiboId") int weiboId, @PathParam("type") String type) {
        if (!Share.types.contains(type)) {
            return ResponseModel.buildParameterError();
        }
        Weibo weibo = WeiboFactory.findById(weiboId);
        if (weibo == null) {
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        Share share = FavorFactory.share(weibo, self, type);
        if (share == null) {
            return ResponseModel.buildServiceError();
        }

        return ResponseModel.buildOk(new FavorCard(share, false));
    }

    //赞我的
    @GET
    @Path("/zanMe/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<FavorCard>> zanMe(@PathParam("min") long min, @PathParam ("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Favor> zans = FavorFactory.zanMe(getSelf(), minTime, maxTime);
        List<FavorCard> cards = zans.stream()
                                       .map(zan -> new FavorCard(zan, false))
                                       .collect(Collectors.toList());

        return ResponseModel.buildOk(cards);
    }

    //我赞的
    @GET
    @Path("/meZan/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<FavorCard>> meZan(@PathParam("min") long min, @PathParam ("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Favor> zans = FavorFactory.meZan(getSelf(), minTime, maxTime);
        List<FavorCard> cards = zans.stream()
                                    .map(zan -> new FavorCard(zan, false))
                                    .collect(Collectors.toList());

        return ResponseModel.buildOk(cards);
    }
}

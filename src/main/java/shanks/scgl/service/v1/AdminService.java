package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.Order;
import shanks.scgl.bean.db.User;
import shanks.scgl.factory.*;
import shanks.scgl.service.BaseService;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;

@Path("/admin")
public class AdminService extends BaseService {

    /**
     * 开通VIP
     *
     * @param uid    用户编号
     * @param length VIP时长
     * @return 是否成功
     */
    @PUT
    @Path("/openVIP/{uid}/{length}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel openVip(@PathParam("uid") int uid, @PathParam("length") int length) {
        User self = getSelf();
        if (self.getId() != 1) {
            return ResponseModel.buildParameterError();
        }

        if (length > 400) {
            return ResponseModel.buildParameterError();
        }

        User user = UserFactory.findById(uid);
        if (user == null) {
            return ResponseModel.buildParameterError();
        }

        LocalDateTime start = user.getVipTime();
        if (start.isBefore(LocalDateTime.now())) {
            start = LocalDateTime.now();
        }
        LocalDateTime end = start.plusDays(length);

        user.setVipTime(end);
        UserFactory.update(user);

        PushFactory.pushOpenVIP(user, new UserCard(user));

        //有手机号
        if (!Strings.isNullOrEmpty(user.getPhone()))
            Notify3rdFactory.sendOpenVIP(user.getPhone(), length);

        //记录订单信息
        Order order = new Order(50, "VIP-388", 0,user);
        OrderFactory.saveOrUpdate(order);

        return ResponseModel.buildOk();
    }

    @POST
    @Path("/sysMsg")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel sysMsg(String content) {
        User self = getSelf();
        if (self.getId() != 1) {
            return ResponseModel.buildParameterError();
        }

        if (Strings.isNullOrEmpty(content)) {
            return ResponseModel.buildParameterError();
        }

        AdminFactory.sendSysMsg(self, content);

        return ResponseModel.buildOk();
    }
}

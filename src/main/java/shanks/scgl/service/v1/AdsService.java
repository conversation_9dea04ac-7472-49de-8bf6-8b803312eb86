package shanks.scgl.service.v1;

import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.service.BaseService;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.List;

@Path("/ads")
public class AdsService extends BaseService {

    /**
     * 废弃,不再使用
     */
    @Deprecated
    @GET
    @Path("/{max0}/{max1}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List> getAds(@PathParam("max0") int max0, @PathParam("max1") int max1) {
        return ResponseModel.buildOk(new ArrayList());
    }
}

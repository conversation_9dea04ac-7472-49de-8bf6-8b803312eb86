package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.anthology.AnthCreateModel;
import shanks.scgl.bean.api.anthology.RecoCreateModel;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.card.AnthCard;
import shanks.scgl.bean.card.KeepCard;
import shanks.scgl.bean.card.RecoCard;
import shanks.scgl.bean.card.WeiboCard;
import shanks.scgl.bean.db.*;
import shanks.scgl.factory.*;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.Constant;
import shanks.scgl.utils.TimeUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Path("/anth")
public class AnthService extends BaseService {
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AnthCard> push(AnthCreateModel model) {
        if (!AnthCreateModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }
        User self = getSelf();
        Anthology anthology;
        if (Strings.isNullOrEmpty(model.getId())) {
            anthology = new Anthology(self, model);
        } else {
            anthology = AnthFactory.findById(model.getId());
            if (anthology == null) {
                return ResponseModel.buildParameterError();
            }
            anthology.update(model);
        }

        anthology = AnthFactory.saveOrUpdate(anthology);


        return ResponseModel.buildOk(new AnthCard(anthology));
    }

    @GET
    @Path("/{uid}/{type}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<AnthCard>> anthRepo(@PathParam("uid") int uid, @PathParam("type") int type,
                                                  @PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        User user = UserFactory.findById(uid);
        if (user == null) {
            return ResponseModel.buildParameterError();
        }
        List<Anthology> anths = AnthFactory.list(user, type, minTime, maxTime);
        List<AnthCard> cards = new ArrayList<>();
        for (Anthology anth : anths) {
            cards.add(new AnthCard(anth));
        }
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AnthCard> getAnth(@PathParam("id") String id) {
        if (Strings.isNullOrEmpty(id)) {
            return ResponseModel.buildParameterError();
        }
        Anthology anthology = AnthFactory.findById(id);

        if (anthology == null) {
            return ResponseModel.buildNotFoundError(ResponseModel.ERROR_NOT_FOUND_ANTH);
        }

        //首次拜访时,可以增加,以后就不再增加.否则没有详细记录访问日志,可以刷人气
        //if (VisitFactory.find(anthology.getUid(), getSelf().getId()) == null)
        //anthology = AnthFactory.addPopular(anthology, 1);

        return ResponseModel.buildOk(new AnthCard(anthology));
    }

    @GET
    @Path("/origin/{anthId}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<WeiboCard>> originList(@PathParam("anthId") String anthId, @PathParam("min") long min,
                                                     @PathParam("max") long max) {
        if (Strings.isNullOrEmpty(anthId)) {
            return ResponseModel.buildParameterError();
        }

        Anthology anthology = AnthFactory.findById(anthId);
        if (anthology == null) {
            return ResponseModel.buildNotFoundError(ResponseModel.ERROR_NOT_FOUND_ANTH);
        }

        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Weibo> weibos = AnthFactory.postOfOrigin(anthology, minTime, maxTime);
        UserFollow follow = UserFactory.getUserFollow(getSelf().getId(), anthology.getUid());
        boolean isRelated = follow != null || anthology.getUid() == getSelf().getId();
        List<WeiboCard> cards = weibos.stream()
                                      .map(weibo -> new WeiboCard(weibo, isRelated))
                                      .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);

    }

    @GET
    @Path("/keep/{anthId}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<KeepCard>> keepList(@PathParam("anthId") String anthId, @PathParam("min") long min,
                                                  @PathParam("max") long max) {
        if (Strings.isNullOrEmpty(anthId)) {
            return ResponseModel.buildParameterError();
        }

        Anthology anthology = AnthFactory.findById(anthId);
        if (anthology == null) {
            return ResponseModel.buildNotFoundError(ResponseModel.ERROR_NOT_FOUND_ANTH);
        }

        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Keep> keeps = AnthFactory.keeps(anthology, minTime, maxTime);

        List<KeepCard> cards = keeps.stream()
                                    .map(KeepCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);

    }

    @DELETE
    @Path("{anthId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AnthCard> delete(@PathParam("anthId") String anthId) {
        if (Strings.isNullOrEmpty(anthId)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        Anthology anth = AnthFactory.findById(anthId);
        if (anth == null) {
            return ResponseModel.buildNotFoundError(ResponseModel.ERROR_NOT_FOUND_ANTH);
        }

        if (anth.getUid() != self.getId()) {
            return ResponseModel.buildNoPermissionError();
        }
        //文集必须为空才能删除
        if (AnthFactory.getAnthSize(anth) != 0) {
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_ANTH_NOT_EMPTY);
        }

        //软删除
        anth = AnthFactory.delete(anth);

        return ResponseModel.buildOk(new AnthCard(anth));
    }

    //有点像add操作
    @PUT
    @Path("/origin/{anthId}/{weiboId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<WeiboCard> moveWeibo(@PathParam("anthId") String anthId,
                                              @PathParam("weiboId") int weiboId) {
        if (Strings.isNullOrEmpty(anthId) || weiboId == 0) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        Anthology anth = AnthFactory.findById(anthId);
        if (anth == null) {
            return ResponseModel.buildNotFoundError(ResponseModel.ERROR_NOT_FOUND_ANTH);
        }
        User self = getSelf();
        //移动的目标只能是自己拥有的
        if (anth.getUid() != self.getId()) {
            return ResponseModel.buildNoPermissionError();
        }
        //最多100条
        if (AnthFactory.getAnthSize(anth) >= Constant.MAX_ANTH_SIZE) {
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_ANTH_IS_FULL);
        }

        Weibo weibo = WeiboFactory.findById(weiboId);
        if (weibo == null) {
            return ResponseModel.buildNotFoundWeiboError(null);
        }
        if (weibo.getUid() != self.getId()) {
            //自己的作品才能加到自己的文集中
            return ResponseModel.buildNoPermissionError();
        }
        weibo = AnthFactory.move(weibo, anth);

        return ResponseModel.buildOk(new WeiboCard(weibo, true));
    }

    @PUT
    @Path("/keep/{anthId}/{keepId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<KeepCard> moveKeep(@PathParam("anthId") String anthId,
                                            @PathParam("keepId") int keepId) {
        if (Strings.isNullOrEmpty(anthId) || keepId == 0) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        Anthology anth = AnthFactory.findById(anthId);
        if (anth == null) {
            return ResponseModel.buildNotFoundError(ResponseModel.ERROR_NOT_FOUND_ANTH);
        }
        User self = getSelf();
        //移动的目标只能是自己拥有的
        if (anth.getUid() != self.getId()) {
            return ResponseModel.buildNoPermissionError();
        }
        //最多100条
        if (AnthFactory.getAnthSize(anth) >= Constant.MAX_ANTH_SIZE) {
            return ResponseModel.buildBusinessError(ResponseModel.ERROR_BUSINESS_ANTH_IS_FULL);
        }

        Keep keep = AnthFactory.getKeep(keepId);
        if (keep == null) {
            //没有找到收藏的记录
            return ResponseModel.buildNotFoundError(ResponseModel.ERROR_NOT_FOUND_KEEP);
        }
        if (keep.getUid() != self.getId()) {
            return ResponseModel.buildNoPermissionError();
        }

        keep = AnthFactory.move(keep, anth);

        return ResponseModel.buildOk(new KeepCard(keep));
    }

    @POST
    @Path("/reco")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<RecoCard> recommend(RecoCreateModel model) {
        if (!RecoCreateModel.check(model)) {
            //参数异常
            return ResponseModel.buildParameterError();
        }

        Anthology anthology = AnthFactory.findById(model.getAnthId());
        if (anthology == null) {
            return ResponseModel.buildParameterError();
        }

        User self = getSelf();
        if (self.loadInfo().getPoint() <= 0) {
            return ResponseModel.buildNoSufficientPointError();
        }

        Recommend recommend = AnthFactory.recommend(anthology, self, model);
        if (recommend == null) {
            return ResponseModel.buildServiceError();
        }

        RecoCard card = new RecoCard(recommend);

        PushFactory.pushNewReco(self,anthology.getUser(), card);

        return ResponseModel.buildOk(card);
    }

    @GET
    @Path("/reco/{anthId}/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<RecoCard>> recos(@PathParam("anthId") String anthId, @PathParam("min") long min,
                                               @PathParam("max") long max) {
        if (Strings.isNullOrEmpty(anthId)) {
            return ResponseModel.buildParameterError();
        }

        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        Anthology anthology = AnthFactory.findById(anthId);
        if (anthology == null) {
            return ResponseModel.buildNotFoundError(ResponseModel.ERROR_NOT_FOUND_ANTH);
        }

        List<Recommend> recos = AnthFactory.recos(anthology, minTime, maxTime);
        List<RecoCard> cards = recos.stream()
                                    .map(RecoCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/recoMe/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<RecoCard>> recoMe(@PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Recommend> recos = AnthFactory.recoMe(getSelf(), minTime, maxTime);
        List<RecoCard> cards = recos.stream()
                                    .map(RecoCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }

    @GET
    @Path("/meReco/{min}/{max}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<RecoCard>> meReco(@PathParam("min") long min, @PathParam("max") long max) {
        LocalDateTime minTime = TimeUtil.getMinTime(min);
        LocalDateTime maxTime = TimeUtil.getMaxTime(max);

        List<Recommend> recos = AnthFactory.meReco(getSelf(), minTime, maxTime);
        List<RecoCard> cards = recos.stream()
                                    .map(RecoCard::new)
                                    .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }
}

package shanks.scgl.service.v1;

import com.google.common.base.Strings;
import shanks.scgl.bean.api.base.PushCard;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.db.PushHistory;
import shanks.scgl.factory.PushFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.Hib;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 推送服务
 */
@Path("/push")
public class PushService extends BaseService {
    @PUT
    @Path("/arrive/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel arrive(@PathParam("id") String historyId) {
        if (Strings.isNullOrEmpty(historyId)) {
            return ResponseModel.buildParameterError();
        }

        PushHistory history = PushFactory.findById(historyId);
        if (history == null) {
            return ResponseModel.buildParameterError();
        }

        history.setArrivalAt(LocalDateTime.now());
        Hib.queryOnly(session -> session.update(history));

        return ResponseModel.buildOk();
    }

    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<List<PushCard>> unArrives() {
        List<PushHistory> unArrived = PushFactory.getUnArrives(getSelf());
        List<PushCard> cards = unArrived
                .stream()
                .map(history -> {
                    PushCard model = new PushCard(history.getId());
                    model.add(history.getEntityType(), history.getEntity());
                    return model;
                })
                .collect(Collectors.toList());
        return ResponseModel.buildOk(cards);
    }
}

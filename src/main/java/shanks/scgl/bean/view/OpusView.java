package shanks.scgl.bean.view;

import org.hibernate.annotations.Immutable;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Immutable
@Table(name = "SCGL_V_OPUS")
public class OpusView {

    @Id
    @PrimaryKeyJoinColumn
    @Column
    private int id;

    @Column
    private String content;
    @Column
    private int uid;
    @Column
    private String portrait;
    @Column
    private String userName;
    @Column
    private int view;
    @Column
    private int comment;
    @Column
    private int favor;
    @Column
    private int keep;
    @Column
    private int share;
    @Column
    private String anthId;
    @Column
    private String anthName;
    @Column
    private int type;
    @Column
    private String poemId;
    @Column
    private String ruleId;
    @Column
    private LocalDateTime createAt;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getPortrait() {
        return portrait;
    }

    public void setPortrait(String portrait) {
        this.portrait = portrait;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public int getView() {
        return view;
    }

    public void setView(int view) {
        this.view = view;
    }

    public int getComment() {
        return comment;
    }

    public void setComment(int comment) {
        this.comment = comment;
    }

    public int getFavor() {
        return favor;
    }

    public void setFavor(int favor) {
        this.favor = favor;
    }

    public int getKeep() {
        return keep;
    }

    public void setKeep(int keep) {
        this.keep = keep;
    }

    public int getShare() {
        return share;
    }

    public void setShare(int share) {
        this.share = share;
    }

    public String getAnthId() {
        return anthId;
    }

    public void setAnthId(String anthId) {
        this.anthId = anthId;
    }

    public String getAnthName() {
        return anthName;
    }

    public void setAnthName(String anthName) {
        this.anthName = anthName;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getPoemId() {
        return poemId;
    }

    public void setPoemId(String poemId) {
        this.poemId = poemId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }
}

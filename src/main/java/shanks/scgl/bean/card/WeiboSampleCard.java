package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Weibo;

import java.time.LocalDateTime;

/**
 * 微博的样例信息
 *
 * @version 2019-1-1
 */
public class WeiboSampleCard {
    @Expose
    private int id;
    @Expose
    private int keep;
    @Expose
    private int comment;
    @Expose
    private int view;
    @Expose
    private int share;
    @Expose
    private int zan;
    @Expose
    private LocalDateTime createAt;


    public WeiboSampleCard(Weibo weibo) {
        this.id = weibo.getId();
        this.keep = weibo.getKeep();
        this.comment = weibo.getComment();
        this.view = weibo.getView();
        this.share = weibo.getShare();
        this.zan = weibo.getZan();
        this.createAt=weibo.getCreateAt();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getKeep() {
        return keep;
    }

    public void setKeep(int keep) {
        this.keep = keep;
    }

    public int getComment() {
        return comment;
    }

    public void setComment(int comment) {
        this.comment = comment;
    }

    public int getView() {
        return view;
    }

    public void setView(int view) {
        this.view = view;
    }

    public int getShare() {
        return share;
    }

    public void setShare(int share) {
        this.share = share;
    }

    public int getZan() {
        return zan;
    }

    public void setZan(int zan) {
        this.zan = zan;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }
}

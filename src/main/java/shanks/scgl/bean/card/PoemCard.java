package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Poem;

import java.time.LocalDateTime;

public class PoemCard {
    public static int ACTION_NONE = 0;
    public static int ACTION_UPLOAD = 1;
    public static int ACTION_DOWNLOAD = 2;

    @Expose
    private String id;
    @Expose
    private String title;
    @Expose
    private String period;
    @Expose
    private String author;
    @Expose
    private String content;
    @Expose
    private String folder;
    @Expose
    private String ruleId;
    @Expose
    private String hash;
    @Expose
    private int uid;
    @Expose
    private LocalDateTime createAt;
    //服务器端的更新时间
    @Expose
    private LocalDateTime updateAt;
    @Expose
    private int action;

    public PoemCard(Poem poem, int action) {
        this.action = action;

        this.id = poem.getId();
        this.title = poem.getTitle();
        this.period = poem.getPeriod();
        this.author = poem.getAuthor();
        this.content = poem.getContent();
        this.folder = poem.getFolder();
        this.ruleId = poem.getRuleId();
        this.uid = poem.getUid();
        this.hash=poem.getHash();
        this.createAt = poem.getCreateAt();
        this.updateAt = poem.getUpdateAt();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFolder() {
        return folder;
    }

    public void setFolder(String folder) {
        this.folder = folder;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getAction() {
        return action;
    }

    public void setAction(int action) {
        this.action = action;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }
}

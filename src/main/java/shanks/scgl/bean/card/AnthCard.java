package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Anthology;
import shanks.scgl.factory.AnthFactory;

import java.time.LocalDateTime;

/**
 * @version 2018/1/14 Created by book
 */

public class AnthCard {
    @Expose
    private String id;
    @Expose
    private String name;
    @Expose
    private String intro;
    @Expose
    private int type;
    @Expose
    private String cover;
    @Expose
    private int popular;
    @Expose
    private int uid;
    @Expose
    private int size;
    @Expose
    private int reco;
    @Expose
    private boolean isDelete;
    @Expose
    private LocalDateTime createAt;

    public AnthCard(Anthology anthology) {
        this.id = anthology.getId();
        this.name = anthology.getName();
        this.intro = anthology.getIntro();
        this.type = anthology.getType();
        this.cover = anthology.getCover();
        this.popular = anthology.getPopular();
        this.uid = anthology.getUid();
        this.createAt = anthology.getCreateAt();
        this.isDelete = anthology.isDelete() != 0;

        this.size = (int) AnthFactory.getAnthSize(anthology);
        this.reco = (int) AnthFactory.getRecoSize(anthology);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public int getPopular() {
        return popular;
    }

    public void setPopular(int popular) {
        this.popular = popular;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }

    public int getReco() {
        return reco;
    }

    public void setReco(int reco) {
        this.reco = reco;
    }
}

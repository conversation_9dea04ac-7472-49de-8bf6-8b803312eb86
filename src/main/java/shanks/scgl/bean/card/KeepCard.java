package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Keep;

import java.time.LocalDateTime;

public class KeepCard {
    @Expose
    private int id;
    @Expose
    private int uid;
    @Expose
    private String anthId;
    @Expose
    private WeiboCard weiboCard;
    @Expose
    private LocalDateTime updateAt;

    public KeepCard(Keep keep) {
        this.id = keep.getId();
        this.uid = keep.getUid();
        this.anthId = keep.getAnthId();
        this.weiboCard = new WeiboCard(keep.getWeibo(), false);
        this.updateAt = keep.getUpdateAt();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getAnthId() {
        return anthId;
    }

    public void setAnthId(String anthId) {
        this.anthId = anthId;
    }

    public WeiboCard getWeiboCard() {
        return weiboCard;
    }

    public void setWeiboCard(WeiboCard weiboCard) {
        this.weiboCard = weiboCard;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }
}

package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Mark;

import java.time.LocalDateTime;

public class MarkCard {
    @Expose
    private String id;
    @Expose
    private float mark;
    @Expose
    private String version;
    @Expose
    private String content;
    @Expose
    private LocalDateTime createAt = LocalDateTime.now();
    @Expose
    private int uid;
    @Expose
    private String rid;

    public MarkCard(Mark mark) {
        this.id = mark.getId();
        this.mark = mark.getMark();
        this.version = mark.getVersion();
        this.content = mark.getContent();
        this.createAt = mark.getCreateAt();
        this.uid = mark.getUid();
        this.rid = mark.getRid();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public float getMark() {
        return mark;
    }

    public void setMark(float mark) {
        this.mark = mark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }
}

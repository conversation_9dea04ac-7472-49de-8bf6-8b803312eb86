package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Comment;

import java.time.LocalDateTime;

public class CommentCard {
    @Expose
    private int id;
    @Expose
    private String content;
    @Expose
    private int uid;
    @Expose
    private int wid;
    @Expose
    private int wuid;
    @Expose
    private int parent;
    @Expose
    private boolean isDelete;
    @Expose
    private boolean isAtme;
    @Expose
    private LocalDateTime createAt;

    public CommentCard(Comment comment, boolean isAtme) {
        this.isAtme = isAtme;
        this.id = comment.getId();
        this.content = comment.getContent();
        this.uid = comment.getUid();
        this.wid = comment.getWid();
        this.wuid = comment.getWuid();
        this.parent = comment.getParent();
        this.isDelete = comment.isDelete() != 0;
        this.createAt = comment.getCreateAt();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getWid() {
        return wid;
    }

    public void setWid(int wid) {
        this.wid = wid;
    }

    public int getParent() {
        return parent;
    }

    public void setParent(int parent) {
        this.parent = parent;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }

    public boolean isAtme() {
        return isAtme;
    }

    public void setAtme(boolean atme) {
        isAtme = atme;
    }

    public int getWuid() {
        return wuid;
    }

    public void setWuid(int wuid) {
        this.wuid = wuid;
    }
}

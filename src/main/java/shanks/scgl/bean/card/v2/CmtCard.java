package shanks.scgl.bean.card.v2;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;
import shanks.scgl.bean.view.CmtView;

import java.time.LocalDateTime;
import java.util.ArrayList;

public class CmtCard {
    @Expose
    private int id;
    @Expose
    private String content;
    @Expose
    private int uid;
    @Expose
    private String userName;
    @Expose
    private String portrait;
    @Expose
    private int wid;
    @Expose
    private LocalDateTime createAt;
    @Expose
    private ArrayList<CmtCard> children = new ArrayList<>();

    public CmtCard(CmtView comment) {
        this.id = comment.getId();
        this.content = comment.getContent();
        this.uid = comment.getUid();
        this.userName = comment.getUserName();
        this.portrait = comment.getPortrait();
        if (Strings.isNullOrEmpty(this.portrait)) {
            this.portrait = "http://m.shicigl.com/00/default_portrait.png";
        }
        this.wid = comment.getWid();
        this.createAt = comment.getCreateAt();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPortrait() {
        return portrait;
    }

    public void setPortrait(String portrait) {
        this.portrait = portrait;
    }

    public int getWid() {
        return wid;
    }

    public void setWid(int wid) {
        this.wid = wid;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public ArrayList<CmtCard> getChildren() {
        return children;
    }

    public void setChildren(ArrayList<CmtCard> children) {
        this.children = children;
    }

}

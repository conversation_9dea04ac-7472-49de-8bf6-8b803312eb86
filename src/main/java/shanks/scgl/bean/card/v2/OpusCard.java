package shanks.scgl.bean.card.v2;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;
import shanks.scgl.bean.view.OpusView;

import java.time.LocalDateTime;

/**
 * 客户端非缓存方案
 */
public class OpusCard {
    @Expose
    private int id;
    @Expose
    private String content;

    @Expose
    private int uid;
    @Expose
    private String portrait;
    @Expose
    private String userName;

    @Expose
    private int view;
    @Expose
    private int comment;
    @Expose
    private int favor;
    @Expose
    private int keep;
    @Expose
    private int share;

    @Expose
    private String anthId;
    @Expose
    private String anthName;

    @Expose
    private int type;
    @Expose
    private String poemId;
    @Expose
    private String ruleId;
    @Expose
    private LocalDateTime createAt;

    public OpusCard(OpusView opus) {
        this.id = opus.getId();
        this.content = opus.getContent();

        this.uid = opus.getUid();
        this.portrait = opus.getPortrait();
        if (Strings.isNullOrEmpty(this.portrait)) {
            this.portrait = "http://m.shicigl.com/00/default_portrait.png";
        }
        this.userName = opus.getUserName();

        this.keep = opus.getKeep();
        this.comment = opus.getComment();
        this.view = opus.getView();
        this.share = opus.getShare();
        this.favor = opus.getFavor();

        this.anthId = opus.getAnthId();
        this.anthName = opus.getAnthName();

        this.type = opus.getType();
        this.poemId = opus.getPoemId();
        this.ruleId = opus.getRuleId();
        this.createAt = opus.getCreateAt();

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getPortrait() {
        return portrait;
    }

    public void setPortrait(String portrait) {
        this.portrait = portrait;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public int getKeep() {
        return keep;
    }

    public void setKeep(int keep) {
        this.keep = keep;
    }

    public int getComment() {
        return comment;
    }

    public void setComment(int comment) {
        this.comment = comment;
    }

    public int getView() {
        return view;
    }

    public void setView(int view) {
        this.view = view;
    }

    public int getShare() {
        return share;
    }

    public void setShare(int share) {
        this.share = share;
    }

    public int getFavor() {
        return favor;
    }

    public void setFavor(int favor) {
        this.favor = favor;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getAnthId() {
        return anthId;
    }

    public void setAnthId(String anthId) {
        this.anthId = anthId;
    }

    public String getAnthName() {
        return anthName;
    }

    public void setAnthName(String anthName) {
        this.anthName = anthName;
    }

    public String getPoemId() {
        return poemId;
    }

    public void setPoemId(String poemId) {
        this.poemId = poemId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }
}

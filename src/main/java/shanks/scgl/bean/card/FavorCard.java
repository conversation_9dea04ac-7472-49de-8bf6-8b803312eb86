package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.*;

import java.time.LocalDateTime;

/**
 * 将keep,share,zan统一为一种数据类型,返回给客户端
 */
public class FavorCard {
    public final static String TYPE_KEEP = "keep";
    public final static String TYPE_ZAN = "zan";

    @Expose
    private int id;
    @Expose
    private int uid;
    @Expose
    private int wuid;
    @Expose
    private int wid;
    @Expose
    private boolean isDelete;
    @Expose
    private String type;
    @Expose
    private LocalDateTime createAt;

    public FavorCard(Weibo weibo, User user, String type, boolean isDelete, int id) {
        this.uid = user.getId();
        this.wuid = weibo.getUid();
        this.wid = weibo.getId();
        this.type = type;
        this.createAt = LocalDateTime.now();
        this.isDelete = isDelete;
        this.id = id;
    }

    public FavorCard(Keep keep, boolean isDelete) {
        this.uid = keep.getUid();
        this.wuid = keep.getWuid();
        this.wid = keep.getWid();
        this.type = TYPE_KEEP;
        this.createAt = keep.getCreateAt();
        this.isDelete = isDelete;
        this.id = keep.getId();
    }

    public FavorCard(Share share, boolean isDelete) {
        this.uid = share.getUid();
        this.wuid = share.getWuid();
        this.wid = share.getWid();
        this.type = share.getType();
        this.createAt = share.getCreateAt();
        this.isDelete = isDelete;
        this.id = share.getId();
    }

    public FavorCard(Favor zan, boolean isDelete) {
        this.uid = zan.getUid();
        this.wuid = zan.getWuid();
        this.wid = zan.getWid();
        this.type = TYPE_ZAN;
        this.createAt = zan.getCreateAt();
        this.isDelete = isDelete;
        this.id = zan.getId();
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getWuid() {
        return wuid;
    }

    public void setWuid(int wuid) {
        this.wuid = wuid;
    }

    public int getWid() {
        return wid;
    }

    public void setWid(int wid) {
        this.wid = wid;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }
}

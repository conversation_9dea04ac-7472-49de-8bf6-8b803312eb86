package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Visitor;

import java.time.LocalDateTime;

public class VisitCard {
    @Expose
    private String id;
    @Expose
    private int times;
    @Expose
    private LocalDateTime updateAt = LocalDateTime.now();
    @Expose
    private int uid;
    @Expose
    private int vid;

    public VisitCard(Visitor visitor) {
        this.id = visitor.getId();
        this.times = visitor.getTimes();
        this.updateAt = visitor.getUpdateAt();
        this.uid = visitor.getUid();
        this.vid = visitor.getVid();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getVid() {
        return vid;
    }

    public void setVid(int vid) {
        this.vid = vid;
    }
}

package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Sign;
import shanks.scgl.bean.db.User;

import java.time.LocalDateTime;

public class SignCard {
    @Expose
    private int count;
    @Expose
    private LocalDateTime lastSignAt;
    @Expose
    private int point;

    public SignCard(Sign sign,User user) {
        count=sign.getCount();
        lastSignAt = sign.getUpdateAt();
        point=user.loadInfo().getPoint();
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public LocalDateTime getLastSignAt() {
        return lastSignAt;
    }

    public void setLastSignAt(LocalDateTime lastSignAt) {
        this.lastSignAt = lastSignAt;
    }

    public int getPoint() {
        return point;
    }

    public void setPoint(int point) {
        this.point = point;
    }
}

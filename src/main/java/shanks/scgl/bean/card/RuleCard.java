package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Rule;
import shanks.scgl.utils.Hib;

import java.time.LocalDateTime;

/**
 * @version 2017/12/9 Created by book
 */

public class RuleCard {
    @Expose
    private String id;
    @Expose
    private String name;
    @Expose
    private String category;
    @Expose
    private String type;
    @Expose
    private String alias;
    @Expose
    private String recommend;
    @Expose
    private float mark;
    @Expose
    private String help;
    @Expose
    private String log;
    @Expose
    private String hash;
    @Expose
    private int status;
    @Expose
    private int uid;
    @Expose
    private int markCount;
    @Expose
    private int download;
    @Expose
    private LocalDateTime modifyAt;

    public RuleCard(Rule rule) {
        this.id = rule.getId();
        this.name = rule.getName();
        this.category = rule.getCategory();
        this.type = rule.getType();
        this.alias = rule.getAlias();
        this.recommend = rule.getRecommend();
        this.mark = rule.getMark();
        this.download = rule.getDownload();
        this.help = rule.getHelp();
        this.log = rule.getLog();
        this.hash = rule.getHash();
        this.status = rule.getStatus();
        this.uid = rule.getUid();
        this.modifyAt = rule.getUpdateAt();
        Hib.queryOnly(session -> {
            session.load(rule, rule.getId());
            //这个时候仅仅只是进行了数量查询,并没有查询整个集合.
            //要查询集合,必须在session存在的情况下进行遍历或者使用Hibernate.initialize(user.getFollowers)
            this.markCount = rule.getMarks().size();
        });
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public float getMark() {
        return mark;
    }

    public void setMark(float mark) {
        this.mark = mark;
    }

    public String getHelp() {
        return help;
    }

    public void setHelp(String help) {
        this.help = help;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public LocalDateTime getModifyAt() {
        return modifyAt;
    }

    public void setModifyAt(LocalDateTime modifyAt) {
        this.modifyAt = modifyAt;
    }

    public int getMarkCount() {
        return markCount;
    }

    public void setMarkCount(int markCount) {
        this.markCount = markCount;
    }

    public int getDownload() {
        return download;
    }

    public void setDownload(int download) {
        this.download = download;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }
}

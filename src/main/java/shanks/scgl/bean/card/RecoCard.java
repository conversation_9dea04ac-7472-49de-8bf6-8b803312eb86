package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Recommend;

import java.time.LocalDateTime;

public class RecoCard {
    @Expose
    private String id;
    @Expose
    private String content;
    @Expose
    private LocalDateTime createAt = LocalDateTime.now();
    @Expose
    private int uid;
    @Expose
    private String anthId;
    @Expose
    private int auid;

    public RecoCard(Recommend reco) {
        this.id = reco.getId();
        this.content = reco.getContent();
        this.createAt = reco.getCreateAt();
        this.uid = reco.getUid();
        this.anthId = reco.getAnthId();
        this.auid=reco.getAuid();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getAnthId() {
        return anthId;
    }

    public void setAnthId(String anthId) {
        this.anthId = anthId;
    }

    public int getAuid() {
        return auid;
    }

    public void setAuid(int auid) {
        this.auid = auid;
    }
}

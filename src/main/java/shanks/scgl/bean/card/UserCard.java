package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.User;
import shanks.scgl.bean.db.UserInfo;

import java.time.LocalDateTime;

public class UserCard {
    @Expose
    private int id;
    @Expose
    private String name;
    @Expose
    private String portrait;
    @Expose
    private String intro;
    @Expose
    private String sex;
    //关注人数量
    @Expose
    private int follow;
    //用户粉丝的数量
    @Expose
    private int fans;
    //发帖数量
    @Expose
    private int weibo;
    @Expose
    private String location;
    @Expose
    private String birthday;
    @Expose
    private int contribute;
    @Expose
    private int talent;
    @Expose
    private int active;
    @Expose
    private int point;
    //我是否关注了当前的User,在follow表中的id
    @Expose
    private int followId;
    //被当前User关注的follow表中的id,用来判断当前用户是否是我的粉丝
    @Expose
    private int fansId;

    //当前登录的用户是否已将此用户拉黑的Id
    @Expose
    private int blackId;

    //用户信息的最后更新时间
    @Expose
    private LocalDateTime modifyAt;
    @Expose
    private int anthCount;
    @Expose
    private String banner;
    @Expose
    private LocalDateTime vipTime;

    public UserCard(final User user) {
        this(user, 0, 0, 0);
    }

    public UserCard(final User user, int followId, int fansId, int blackId) {
        UserInfo userInfo = user.loadInfo();

        this.followId = followId;
        this.fansId = fansId;
        this.blackId = blackId;

        this.id = user.getId();
        this.name = userInfo.getUserName();
        this.portrait = userInfo.getFace();
        this.intro = userInfo.getIntro();
        this.sex = userInfo.getSex();
        this.modifyAt = userInfo.getUpdateAt();
        this.fans = userInfo.getFans();
        this.follow = userInfo.getFollow();

        this.weibo = userInfo.getWeibo();
        this.birthday = userInfo.getBirthday();
        this.location = userInfo.getLocation();
        this.contribute = userInfo.getContribute();
        this.talent = userInfo.getTalent();
        this.active = userInfo.getActive();
        this.point = userInfo.getPoint();
        this.banner = userInfo.getBanner();

        this.vipTime = user.getVipTime();

        this.anthCount = userInfo.getAnthCount();
    }

    public UserCard() {
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPortrait() {
        return portrait;
    }

    public void setPortrait(String portrait) {
        this.portrait = portrait;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public int getFollow() {
        return follow;
    }

    public void setFollow(int follows) {
        this.follow = follows;
    }

    public int getFans() {
        return fans;
    }

    public void setFans(int fans) {
        this.fans = fans;
    }

    public LocalDateTime getModifyAt() {
        return modifyAt;
    }

    public void setModifyAt(LocalDateTime modifyAt) {
        this.modifyAt = modifyAt;
    }

    public int getWeibo() {
        return weibo;
    }

    public void setWeibo(int weibo) {
        this.weibo = weibo;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public int getContribute() {
        return contribute;
    }

    public void setContribute(int contribute) {
        this.contribute = contribute;
    }

    public int getTalent() {
        return talent;
    }

    public void setTalent(int talent) {
        this.talent = talent;
    }

    public int getPoint() {
        return point;
    }

    public void setPoint(int point) {
        this.point = point;
    }

    public int getActive() {
        return active;
    }

    public void setActive(int active) {
        this.active = active;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getFansId() {
        return fansId;
    }

    public void setFansId(int fansId) {
        this.fansId = fansId;
    }

    public int getFollowId() {
        return followId;
    }

    public void setFollowId(int followId) {
        this.followId = followId;
    }

    public int getAnthCount() {
        return anthCount;
    }

    public void setAnthCount(int anthCount) {
        this.anthCount = anthCount;
    }

    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }

    public LocalDateTime getVipTime() {
        return vipTime;
    }

    public void setVipTime(LocalDateTime vipTime) {
        this.vipTime = vipTime;
    }

    public int getBlackId() {
        return blackId;
    }

    public void setBlackId(int blackId) {
        this.blackId = blackId;
    }
}

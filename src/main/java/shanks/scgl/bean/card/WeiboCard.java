package shanks.scgl.bean.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.Weibo;

import java.time.LocalDateTime;

public class WeiboCard {
    @Expose
    private int id;
    @Expose
    private String content;
    @Expose
    private int keep;
    @Expose
    private int comment;
    @Expose
    private int view;
    @Expose
    private int uid;
    @Expose
    private int share;
    @Expose
    private int zan;
    @Expose
    private int type;
    @Expose
    private String anthId;
    @Expose
    private String poemId;
    @Expose
    private String ruleId;
    @Expose
    private boolean isRelated;
    @Expose
    private boolean isDelete;
    @Expose
    private LocalDateTime createAt;
    @Expose
    private LocalDateTime modifyAt;

    public WeiboCard(Weibo weibo) {
        this(weibo, false);
    }

    public WeiboCard(Weibo weibo, boolean isRelated, boolean isDelete) {
        this(weibo, isRelated);
        this.isDelete = isDelete;
    }

    public WeiboCard(Weibo weibo, boolean isRelated) {
        this.isRelated = isRelated;
        this.id = weibo.getId();
        this.content = weibo.getContent();
        this.keep = weibo.getKeep();
        this.comment = weibo.getComment();
        this.view = weibo.getView();
        this.uid = weibo.getUid();
        this.share = weibo.getShare();
        this.zan = weibo.getZan();
        this.type = weibo.getType();
        this.poemId = weibo.getPoemId();
        this.ruleId = weibo.getRuleId();
        this.createAt = weibo.getCreateAt();
        this.modifyAt = weibo.getUpdateAt();
        this.isDelete = false;
        this.anthId=weibo.getAnthId();

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getKeep() {
        return keep;
    }

    public void setKeep(int keep) {
        this.keep = keep;
    }

    public int getComment() {
        return comment;
    }

    public void setComment(int comment) {
        this.comment = comment;
    }

    public int getView() {
        return view;
    }

    public void setView(int view) {
        this.view = view;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getShare() {
        return share;
    }

    public void setShare(int share) {
        this.share = share;
    }

    public int getZan() {
        return zan;
    }

    public void setZan(int zan) {
        this.zan = zan;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isRelated() {
        return isRelated;
    }

    public void setRelated(boolean related) {
        isRelated = related;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getModifyAt() {
        return modifyAt;
    }

    public void setModifyAt(LocalDateTime modifyAt) {
        this.modifyAt = modifyAt;
    }

    public String getPoemId() {
        return poemId;
    }

    public void setPoemId(String poemId) {
        this.poemId = poemId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }

    public String getAnthId() {
        return anthId;
    }

    public void setAnthId(String anthId) {
        this.anthId = anthId;
    }

}

package shanks.scgl.bean.v3.api.user;

import com.google.gson.annotations.Expose;

public class UpdateUserInfoModel {
    public static final int TYPE_PORTRAIT = 0;
    public static final int TYPE_NAME = 1;
    public static final int TYPE_SEX = 2;
    public static final int TYPE_LOCATION = 3;
    public static final int TYPE_BIRTH = 4;
    public static final int TYPE_INTRO = 5;

    @Expose
    private int type;

    @Expose
    private String value;

    public int getType() {
        return type;
    }

    public String getValue() {
        return value;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static boolean check(UpdateUserInfoModel model) {
        return model != null
                && (model.type == TYPE_PORTRAIT || model.type == TYPE_NAME || model.type == TYPE_SEX || model.type == TYPE_LOCATION || model.type == TYPE_BIRTH || model.type == TYPE_INTRO);
    }
}

package shanks.scgl.bean.v3.api.account;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

/**
 * 注册模型,V3版本API使用
 */

public class RegisterModel {

    public static final int TYPE_WX = 0;
    public static final int TYPE_QQ = 1;

    @Expose
    private int type;

    @Expose
    private String code;

    public void setType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    /**
     * 检查注册信息
     */
    public static boolean check(RegisterModel model) {
        return model != null
                && !Strings.isNullOrEmpty(model.code)
                && (model.type == TYPE_QQ || model.type == TYPE_WX);
    }

}

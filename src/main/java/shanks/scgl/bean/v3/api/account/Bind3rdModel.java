package shanks.scgl.bean.v3.api.account;

import com.google.gson.annotations.Expose;
import com.google.common.base.Strings;

/**
 * 第三方账号绑定请求模型
 */
public class Bind3rdModel {
    // 绑定类型常量
    public static final int TYPE_WX = 0;
    public static final int TYPE_QQ = 1;

    @Expose
    private int type;       // 绑定类型: 1-QQ, 0-微信

    @Expose
    private String code;    // 授权码或OpenID

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 检查请求参数是否有效
     *
     * @param model 请求模型
     * @return 是否有效
     */
    public static boolean check(Bind3rdModel model) {
        return model != null
                && (model.type == TYPE_QQ || model.type == TYPE_WX)
                && !Strings.isNullOrEmpty(model.code);
    }
}

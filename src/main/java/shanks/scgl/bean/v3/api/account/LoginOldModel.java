package shanks.scgl.bean.v3.api.account;

import com.google.gson.annotations.Expose;
import com.google.common.base.Strings;
public class LoginOldModel {
    public static final int TYPE_ACCOUNT_PWD = 0;
    public static final int TYPE_PHONE_PWD = 1;
    public static final int TYPE_PHONE_CODE = 2;

    @Expose
    private int type;

    @Expose
    private String id;

    @Expose
    private String pwd;

    public int getType() {
        return type;
    }

    public String getId() {
        return id;
    }

    public String getPwd() {
        return pwd;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    /**
     * 检查登录信息
     */
    public static boolean check(LoginOldModel model) {
        return model != null
                && (model.type == TYPE_ACCOUNT_PWD || model.type == TYPE_PHONE_PWD || model.type == TYPE_PHONE_CODE)
                && !Strings.isNullOrEmpty(model.id)
                && !Strings.isNullOrEmpty(model.pwd);
    }
}

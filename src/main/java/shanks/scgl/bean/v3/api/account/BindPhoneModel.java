package shanks.scgl.bean.v3.api.account;

import com.google.gson.annotations.Expose;
import com.google.common.base.Strings;
public class BindPhoneModel {
    @Expose
    private String phone;

    @Expose
    private String code;    
    
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }   

    public String getCode() {
        return code;
    }

    public void setCode(String code) {  
        this.code = code;
    }

    public static boolean check(BindPhoneModel model) {
        return model != null && !Strings.isNullOrEmpty(model.phone) && !Strings.isNullOrEmpty(model.code);
    }
}

package shanks.scgl.bean.v3.api.account;

import com.google.gson.annotations.Expose;
import com.google.common.base.Strings;

/**
 * 获取手机验证码的请求模型
 */
public class SendSmsModel {
    // 验证码用途类型
    public static final int TYPE_LOGIN = 1; // 登录验证
    public static final int TYPE_BIND = 2;  // 绑定手机号

    @Expose
    private String phone;

    @Expose
    private int type;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) { 
        this.type = type;
    }

    /**
     * 检查请求参数是否有效
     * 
     * @param model 请求模型
     * @return 是否有效
     */
    public static boolean check(SendSmsModel model) {
        return model != null && !Strings.isNullOrEmpty(model.phone) && (model.type == TYPE_LOGIN || model.type == TYPE_BIND);
    }
} 
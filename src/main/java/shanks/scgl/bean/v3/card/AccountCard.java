package shanks.scgl.bean.v3.card;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.User;

import java.time.LocalDateTime;

public class AccountCard {
    @Expose
    private UserCard userCard;
    //当前登录的账号
    @Expose
    private String account;
    //登录成功后获取的token,可以通过token获取用户的所有信息
    @Expose
    private String token;
    //标识是否已经绑定到了设备PushID
    @Expose
    private String pushId;
    @Expose
    private String phone;
    @Expose
    private String wxId;
    @Expose
    private String qqId;

    @Expose
    private LocalDateTime createAt;

    public AccountCard(User user) {
        this.userCard = new UserCard(user);
        this.account = user.getAccount();
        this.token = user.getToken();
        this.pushId = user.getPushId();
        this.phone=user.getPhone();
        this.wxId=user.getWxId();
        this.qqId=user.getQqId();
        this.createAt =user.getCreateAt();
    }

    public UserCard getUserCard() {
        return userCard;
    }

    public void setUserCard(UserCard userCard) {
        this.userCard = userCard;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getWxId() {
        return wxId;
    }

    public void setWxId(String wxId) {
        this.wxId = wxId;
    }

    public String getQqId() {
        return qqId;
    }

    public void setQqId(String qqId) {
        this.qqId = qqId;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }
}

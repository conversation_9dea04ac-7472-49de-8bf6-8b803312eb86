package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "SCGL_VISITOR")
public class Visitor {
    @Id
    @PrimaryKeyJoinColumn
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @Column(updatable = false, nullable = false)
    private String id;

    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();
    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    //被访问者
    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User visited;

    //访问者
    @Column(nullable = false, updatable = false, insertable = false)
    private int vid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "vid")
    private User visitor;

    @Column
    private int times;

    public Visitor() {
    }

    public Visitor(User visited, User visitor) {
        this.visited = visited;
        this.visitor = visitor;

        this.uid = visited.getId();
        this.vid = visitor.getId();
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getVisited() {
        return visited;
    }

    public void setVisited(User visited) {
        this.visited = visited;
    }

    public int getVid() {
        return vid;
    }

    public void setVid(int vid) {
        this.vid = vid;
    }

    public User getVisitor() {
        return visitor;
    }

    public void setVisitor(User visitor) {
        this.visitor = visitor;
    }

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }
}

package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;
import shanks.scgl.bean.api.rule.RuleCreateModel;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "SCGL_RULE")
public class Rule {
    public static final int STATUS_UN_APPROVED = 0;
    public static final int STATUS_ON_LINE = 1;
    public static final int STATUS_OFF_LINE = 2;
    public static final int STATUS_FORBIDDEN = 3;

    @Id
    @PrimaryKeyJoinColumn
    @Column(updatable = false, nullable = false)
    private String id;
    @Column
    private String name;
    @Column
    private String category;
    @Column
    private String type;
    @Column
    private String alias;
    @Column
    private String recommend;
    @Column
    private float mark;
    @Column
    private int download;
    @Column(columnDefinition = "TEXT")
    private String help;
    @Column(columnDefinition = "TEXT")
    private String log;
    @Column
    private String hash;
    @Column
    private int status;

    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();

    //不用更新时间戳,评分或以后加下载次数都会更新数据
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    @JoinColumn(name = "rid")
    //懒加载,默认加载Rule的时候并不查询这个集合
    @LazyCollection(LazyCollectionOption.EXTRA)
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<Mark> marks = new ArrayList<>();

    public Rule() {
    }

    public Rule(RuleCreateModel model, User user) {
        this.id = model.getId();
        this.name = model.getName();
        this.category = model.getCategory();
        this.type = model.getType();
        this.alias = model.getAlias();
        this.recommend = model.getRecommend();
        this.help = model.getHelp();
        this.log = model.getLog();

        this.uid = user.getId();
        this.user = user;

        this.hash = model.getClientHash();
        //新增的暂不审核
        this.status = STATUS_ON_LINE;
        this.mark = 3;
    }

    public void update(RuleCreateModel model) {
        this.name = model.getName();
        this.category = model.getCategory();
        this.type = model.getType();
        this.alias = model.getAlias();
        this.recommend = model.getRecommend();
        this.help = model.getHelp();
        this.log = model.getLog();

        //设置更新时间
        this.updateAt = LocalDateTime.now();
        this.hash = model.getClientHash();

        //如果是下线或上线状态,直接上线.如果是审核中或者禁用,设置为审核中.
        switch (this.status) {
            case STATUS_ON_LINE:
            case STATUS_OFF_LINE:
                this.status = STATUS_ON_LINE;
                break;
            case STATUS_UN_APPROVED:
            case STATUS_FORBIDDEN:
                this.status = STATUS_UN_APPROVED;
                break;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public float getMark() {
        return mark;
    }

    public void setMark(float mark) {
        this.mark = mark;
    }

    public String getHelp() {
        return help;
    }

    public void setHelp(String help) {
        this.help = help;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public List<Mark> getMarks() {
        return marks;
    }

    public void setMarks(List<Mark> marks) {
        this.marks = marks;
    }

    public int getDownload() {
        return download;
    }

    public void setDownload(int download) {
        this.download = download;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }
}

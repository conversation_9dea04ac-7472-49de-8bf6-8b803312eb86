package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "SCGL_ATME")
public class Atme {
    @Id
    @PrimaryKeyJoinColumn
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(updatable = false, nullable = false)
    private int id;

    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false,fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    @Column(nullable = false, updatable = false, insertable = false)
    private int wid;
    @ManyToOne(optional = false,fetch = FetchType.LAZY)
    @JoinColumn(name = "wid")
    private Weibo weibo;

    @Column(nullable = false, updatable = false, insertable = false)
    private int cid;
    @ManyToOne(optional = false,fetch = FetchType.LAZY)
    @JoinColumn(name = "cid")
    private Comment comment;

    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();

    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    public Atme() {
    }

    public Atme(User user, Weibo weibo, Comment comment) {
        this.user = user;
        this.weibo = weibo;
        this.comment = comment;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public int getWid() {
        return wid;
    }

    public void setWid(int wid) {
        this.wid = wid;
    }

    public Weibo getWeibo() {
        return weibo;
    }

    public void setWeibo(Weibo weibo) {
        this.weibo = weibo;
    }


    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getCid() {
        return cid;
    }

    public void setCid(int cid) {
        this.cid = cid;
    }

    public Comment getComment() {
        return comment;
    }

    public void setComment(Comment comment) {
        this.comment = comment;
    }
}

package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import shanks.scgl.bean.api.weibo.CommentCreateModel;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "SCGL_COMMENT")
public class Comment {
    @Id
    @PrimaryKeyJoinColumn
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(updatable = false, nullable = false)
    private int id;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;

    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    @Column(nullable = false, updatable = false, insertable = false)
    private int wid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "wid")
    private Weibo weibo;

    //冗余一个字段,否则查询评论我的比较消耗性能
    @Column(nullable = false, updatable = false, insertable = false)
    private int wuid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "wuid")
    private User weiboUser;

    @Column(nullable = false, updatable = false)
    private int parent;

    //是否已经被删除
    @Column
    private int isDelete;

    //评论时所用的IP地址
    @Column
    private String ipAddr;

    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();

    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    public Comment() {
    }

    public Comment(User user, Weibo weibo, CommentCreateModel model,String ipAddr) {
        content = model.getContent();
        parent = model.getParent();

        this.wid=weibo.getId();
        this.weibo = weibo;

        this.wuid=weibo.getUid();
        this.weiboUser=weibo.getUser();

        this.uid=user.getId();
        this.user = user;

        this.ipAddr=ipAddr;

        isDelete = 0;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public int getWid() {
        return wid;
    }

    public void setWid(int wid) {
        this.wid = wid;
    }

    public Weibo getWeibo() {
        return weibo;
    }

    public void setWeibo(Weibo weibo) {
        this.weibo = weibo;
    }

    public int getParent() {
        return parent;
    }

    public void setParent(int parent) {
        this.parent = parent;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int isDelete() {
        return isDelete;
    }

    public void setDelete(int delete) {
        isDelete = delete;
    }

    public int getWuid() {
        return wuid;
    }

    public void setWuid(int wuid) {
        this.wuid = wuid;
    }

    public User getWeiboUser() {
        return weiboUser;
    }

    public void setWeiboUser(User weiboUser) {
        this.weiboUser = weiboUser;
    }

    public String getIpAddr() {
        return ipAddr;
    }

    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }
}

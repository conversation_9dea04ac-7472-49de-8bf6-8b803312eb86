package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;
import shanks.scgl.bean.api.anthology.RecoCreateModel;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "SCGL_RECOMMEND")
public class Recommend {
    @Id
    @PrimaryKeyJoinColumn
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @Column(updatable = false, nullable = false)
    private String id;

    @Column(columnDefinition = "TEXT")
    private String content;

    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();
    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    @Column(nullable = false, updatable = false, insertable = false)
    private String anthId;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "anthId")
    private Anthology anthology;

    @Column(nullable = false, updatable = false, insertable = false)
    private int auid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "auid")
    private User anthOwner;

    public Recommend() {
    }

    public Recommend(RecoCreateModel model, User user, User anthOwner, Anthology anth) {
        this.content = model.getContent();

        this.anthology = anth;
        this.user = user;
        this.anthOwner=anthOwner;

        this.anthId = anth.getId();
        this.uid = user.getId();
        this.auid=anthOwner.getId();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getAnthId() {
        return anthId;
    }

    public void setAnthId(String anthId) {
        this.anthId = anthId;
    }

    public Anthology getAnthology() {
        return anthology;
    }

    public void setAnthology(Anthology anthology) {
        this.anthology = anthology;
    }

    public int getAuid() {
        return auid;
    }

    public void setAuid(int auid) {
        this.auid = auid;
    }

    public User getAnthOwner() {
        return anthOwner;
    }

    public void setAnthOwner(User anthOwner) {
        this.anthOwner = anthOwner;
    }
}

package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import shanks.scgl.bean.api.weibo.WeiboCreateModel;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "SCGL_WEIBO")
public class Weibo {
    @Id
    @PrimaryKeyJoinColumn
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(updatable = false, nullable = false)
    private int id;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;
    @Column
    private int keep;
    @Column
    private int comment;
    @Column
    private int view;

    //默认是立即加载,不需要每次都加载用户信息.访问频率比较大
    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    @Column
    private int share;
    @Column
    private int zan;
    @Column
    private int type;

    //新增字段,关联的诗词必须唯一
    @Column(unique = true)
    private String poemId;
    @Column
    private String ruleId;

    @Column(updatable = false, insertable = false)
    private String anthId;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "anthId")
    private Anthology anthology;

    //发布时所用的IP地址
    @Column
    private String ipAddr;

    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();

    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    public Weibo() {
    }

    public Weibo(User owner, Anthology anth, WeiboCreateModel model,String ipAddr) {
        type = model.getType();
        content = model.getContent();
        this.user = owner;
        this.uid = owner.getId();
        poemId = model.getPoemId();
        ruleId = model.getRuleId();
        this.anthId = model.getAnthId();
        this.anthology = anth;
        this.ipAddr=ipAddr;
    }

    public void update(WeiboCreateModel model,Anthology anthology,String ipAddr) {
        type = model.getType();
        content = model.getContent();
        poemId = model.getPoemId();
        ruleId = model.getRuleId();

        this.anthId = model.getAnthId();
        this.anthology = anthology;
        this.ipAddr=ipAddr;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getKeep() {
        return keep;
    }

    public void setKeep(int keep) {
        this.keep = keep;
    }

    public int getComment() {
        return comment;
    }

    public void setComment(int comment) {
        this.comment = comment;
    }

    public int getView() {
        return view;
    }

    public void setView(int view) {
        this.view = view;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getShare() {
        return share;
    }

    public void setShare(int share) {
        this.share = share;
    }

    public int getZan() {
        return zan;
    }

    public void setZan(int zan) {
        this.zan = zan;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getPoemId() {
        return poemId;
    }

    public void setPoemId(String poemId) {
        this.poemId = poemId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getAnthId() {
        return anthId;
    }

    public void setAnthId(String aid) {
        this.anthId = aid;
    }

    public Anthology getAnthology() {
        return anthology;
    }

    public void setAnthology(Anthology anthology) {
        this.anthology = anthology;
    }

    public String getIpAddr() {
        return ipAddr;
    }

    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }
}

package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "SCGL_KEEP")
public class Keep {
    @Id
    @PrimaryKeyJoinColumn
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(updatable = false, nullable = false)
    private int id;


    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    @Column(nullable = false, updatable = false, insertable = false)
    private int wuid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "wuid")
    private User weiboUser;

    @Column(nullable = false, updatable = false, insertable = false)
    private int wid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "wid")
    private Weibo weibo;

    @Column(updatable = false, insertable = false)
    private String anthId;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "anthId")
    private Anthology anthology;

    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();

    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public int getWuid() {
        return wuid;
    }

    public void setWuid(int wuid) {
        this.wuid = wuid;
    }

    public User getWeiboUser() {
        return weiboUser;
    }

    public void setWeiboUser(User weiboUser) {
        this.weiboUser = weiboUser;
    }

    public int getWid() {
        return wid;
    }

    public void setWid(int wid) {
        this.wid = wid;
    }

    public Weibo getWeibo() {
        return weibo;
    }

    public void setWeibo(Weibo weibo) {
        this.weibo = weibo;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public String getAnthId() {
        return anthId;
    }

    public void setAnthId(String anthId) {
        this.anthId = anthId;
    }

    public Anthology getAnthology() {
        return anthology;
    }

    public void setAnthology(Anthology anthology) {
        this.anthology = anthology;
    }
}

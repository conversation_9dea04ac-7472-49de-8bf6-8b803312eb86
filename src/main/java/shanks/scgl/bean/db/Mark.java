package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;
import shanks.scgl.bean.api.rule.MarkCreateModel;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "SCGL_MARK")
public class Mark {
    @Id
    @PrimaryKeyJoinColumn
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @Column(updatable = false, nullable = false)
    private String id;

    @Column
    private float mark;
    @Column
    private String version;
    @Column(columnDefinition = "TEXT")
    private String content;

    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();
    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    //冗余一个字段,否则查询时比较消耗性能
    @Column(nullable = false, updatable = false, insertable = false)
    private int ruid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "ruid")
    private User ruleOwner;

    @Column(nullable = false, updatable = false, insertable = false)
    private String rid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "rid")
    private Rule rule;

    public Mark() {
    }

    public Mark(MarkCreateModel model, User user, Rule rule) {
        this.mark = model.getMark();
        this.version = model.getVersion();
        this.content = model.getContent();

        this.rule = rule;
        this.user = user;
        this.ruleOwner=rule.getUser();

        this.rid = rule.getId();
        this.uid = user.getId();
        this.ruid=rule.getUid();
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public float getMark() {
        return mark;
    }

    public void setMark(float mark) {
        this.mark = mark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public Rule getRule() {
        return rule;
    }

    public void setRule(Rule rule) {
        this.rule = rule;
    }

    public int getRuid() {
        return ruid;
    }

    public void setRuid(int ruid) {
        this.ruid = ruid;
    }

    public User getRuleOwner() {
        return ruleOwner;
    }

    public void setRuleOwner(User ruleOwner) {
        this.ruleOwner = ruleOwner;
    }
}

package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import shanks.scgl.bean.api.account.LaunchModel;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "SCGL_LAUNCH")
public class Launch {
    @Id
    @Column(updatable = false, nullable = false)
    private String id;

    //可以为空
    @Column
    private int uid;

    @Column
    private String osVersion;

    @Column
    private String osModel;

    @Column
    private String appVersion;

    @Column
    private LocalDateTime launchAt;

    //目前冗余
    @CreationTimestamp
    @Column(columnDefinition = "DATETIME")
    private LocalDateTime createAt = LocalDateTime.now();

    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME")
    private LocalDateTime updateAt = LocalDateTime.now();

    public Launch(User user, LaunchModel model) {
        if (user != null) {
            uid=user.getId();
        }
        this.launchAt=LocalDateTime.now();

        this.id=model.getId();
        this.appVersion=model.getAppVersion();
        this.osModel=model.getOsModel();
        this.osVersion=model.getOsVersion();
    }

    public void update(User user, LaunchModel model) {
        if (user != null) {
            uid=user.getId();
        }
        this.launchAt=LocalDateTime.now();

        this.appVersion=model.getAppVersion();
        this.osModel=model.getOsModel();
        this.osVersion=model.getOsVersion();
    }

    public Launch() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getOsModel() {
        return osModel;
    }

    public void setOsModel(String osModel) {
        this.osModel = osModel;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public LocalDateTime getLaunchAt() {
        return launchAt;
    }

    public void setLaunchAt(LocalDateTime launchAt) {
        this.launchAt = launchAt;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }
}

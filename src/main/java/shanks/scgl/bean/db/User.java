package shanks.scgl.bean.db;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import shanks.scgl.factory.UserFactory;

import javax.persistence.*;
import java.security.Principal;
import java.time.LocalDateTime;

/**
 * 以前将账号和用户信息分成两个表,兼容历史数据,不进行变更.
 */
@Entity
@Table(name = "SCGL_USER")
public class User implements Principal {
    //int类型,历史原因
    @Id
    @PrimaryKeyJoinColumn
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(updatable = false, nullable = false)
    private int id;

    //关联的用户信息
    //用下面这个注解避免再生成一个新的列
    @PrimaryKeyJoinColumn
    @OneToOne(cascade = {CascadeType.ALL}, fetch = FetchType.LAZY, optional = false)
    private UserInfo userInfo;

    //账号是唯一标识
    @Column(nullable = false, length = 128, unique = true)
    private String account;

    //密码不为空
    @Column(nullable = false)
    private String password;

    //以前是lock,关键字冲突
    @Column
    private int locked = 0;
    /**
     * 电话唯一
     */
    @Column(length = 62, unique = true)
    private String phone;

    //token可以拉取用户信息,必须唯一
    @Column(unique = true)
    private String token;

    //用于推送的设备ID
    @Column(unique = true)
    private String pushId;

    //绑定的QQ唯一编号
    @Column(unique = true)
    private String qqId;

    //绑定的新浪唯一编号
    @Column(unique = true)
    private String sinaId;

    //TODO 删除邮件相关的操作，暂时保留
    @Column(unique = true)
    private String email;

    //微信唯一编号
    @Column(unique = true)
    private String wxId;

    //VIP有效期,默认是当前日期的前一天
    @Column(columnDefinition = "DATETIME")
    private LocalDateTime vipTime = LocalDateTime.now().minusDays(1);

    //创建时间
    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();
    //更新时间
    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    @Override
    public String getName() {
        //Principal接口,account标识唯一一个用户
        return account;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getLocked() {
        return locked;
    }

    public void setLocked(int locked) {
        this.locked = locked;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    public String getQqId() {
        return qqId;
    }

    public void setQqId(String qqId) {
        this.qqId = qqId;
    }

    public String getSinaId() {
        return sinaId;
    }

    public void setSinaId(String sinaId) {
        this.sinaId = sinaId;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public UserInfo getUserInfo() {
        return userInfo;
//      return   UserFactory.getUserInfo(this);
    }

    /**
     * UserInfo是懒加载,如果想确保一定有实体值,调用此方法
     * @return UserInfo
     */
    public UserInfo loadInfo(){
        userInfo= UserFactory.getUserInfo(this);
        return userInfo;
//        return UserFactory.getUserInfo(this);
    }

    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
    }

    public LocalDateTime getVipTime() {
        return vipTime;
    }

    public void setVipTime(LocalDateTime vipTime) {
        this.vipTime = vipTime;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWxId() {
        return wxId;
    }

    public void setWxId(String wxId) {
        this.wxId = wxId;
    }
}

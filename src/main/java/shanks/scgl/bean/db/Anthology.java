package shanks.scgl.bean.db;

import com.google.common.base.Strings;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;
import shanks.scgl.bean.api.anthology.AnthCreateModel;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "SCGL_ANTHOLOGY")
public class Anthology {
    public static final int TYPE_SELF = 0;
    public static final int TYPE_KEEP = 1;

    @Id
    @PrimaryKeyJoinColumn
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @Column(updatable = false, nullable = false)
    private String id;
    @Column
    private String name;
    @Column
    private String intro;
    @Column
    private int type;
    @Column
    private String cover;
    @Column
    private int popular;
    @Column
    private int isDelete;

    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false,fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    @CreationTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();

    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    public Anthology() {
    }

    public Anthology(User self, AnthCreateModel model) {
        this.cover = model.getCover();
        this.name = model.getName();
        this.intro = model.getIntro();
        this.type = model.getType();

        this.user = self;
    }

    public void update(AnthCreateModel model) {
        if (!Strings.isNullOrEmpty(model.getName()))
            this.name = model.getName();
        if (!Strings.isNullOrEmpty(model.getIntro()))
            this.intro = model.getIntro();
        if (!Strings.isNullOrEmpty(model.getCover()))
            this.cover = model.getCover();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public int getPopular() {
        return popular;
    }

    public void setPopular(int popular) {
        this.popular = popular;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int isDelete() {
        return isDelete;
    }

    public void setDelete(int delete) {
        isDelete = delete;
    }
}

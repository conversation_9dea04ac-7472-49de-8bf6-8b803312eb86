package shanks.scgl.bean.db;

import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

@Entity
@Table(name = "SCGL_ORDER")
public class Order {
    @Id
    @PrimaryKeyJoinColumn
    @Column(updatable = false, nullable = false)
    private String id;
    @Column
    private float amount;
    @Column
    private String content;
    @Column
    private int status;

    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();
    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS", Locale
            .ENGLISH);

    public Order() {
    }

    public Order(float amount, String content, int status,User user) {
        this.id = LocalDateTime.now().format(FORMATTER);
        this.amount = amount;
        this.content = content;
        this.status = status;
        this.user=user;
        this.uid=user.getId();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public float getAmount() {
        return amount;
    }

    public void setAmount(float amount) {
        this.amount = amount;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}

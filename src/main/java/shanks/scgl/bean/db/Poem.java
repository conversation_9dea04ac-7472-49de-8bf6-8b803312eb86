package shanks.scgl.bean.db;

import org.hibernate.annotations.UpdateTimestamp;
import shanks.scgl.bean.api.poem.PoemCreateModel;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Entity
@Table(name = "SCGL_POEM")
public class Poem {
    @Id
    @PrimaryKeyJoinColumn
    @Column(updatable = false, nullable = false)
    private String id;
    @Column
    private String title;
    @Column
    private String period;
    @Column
    private String author;
    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;
    @Column
    private String folder;
    @Column
    private String ruleId;
    @Column
    private String hash;

    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime createAt = LocalDateTime.now();
    @UpdateTimestamp
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime updateAt = LocalDateTime.now();

    @Column(nullable = false, updatable = false, insertable = false)
    private int uid;
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "uid")
    private User user;

    public Poem() {
    }

    public Poem(PoemCreateModel model, User user) {
        this.id = model.getId();
        this.title = model.getTitle();
        this.period = model.getPeriod();
        this.author = model.getAuthor();
        this.content = model.getContent();
        this.folder = model.getFolder();
        this.ruleId = model.getRuleId();

        //客户端上传的时间，只有日期，没有时分秒
        this.createAt = model.getCreateAt();
        adjustCreateAt();

        this.hash = model.getClientHash();

        this.uid = user.getId();
        this.user = user;

        checkFields();
    }

    public void update(PoemCreateModel model) {
        this.title = model.getTitle();
        this.period = model.getPeriod();
        this.author = model.getAuthor();
        this.content = model.getContent();
        this.folder = model.getFolder();
        this.ruleId = model.getRuleId();
        this.hash = model.getClientHash();

        //todo 创建时间无法更新,诗稿格式统一的时候再考虑

        //强制刷新一下时间,如果内容没有变更,时间戳不会改变
        this.updateAt = LocalDateTime.now();

        checkFields();
    }

    private void adjustCreateAt() {
        if (this.createAt.getHour() == 0 && this.createAt.getMinute() == 0 && this.createAt.getSecond() == 0
                && this.createAt.getNano() == 0) {
            this.createAt = this.createAt.with(LocalTime.now());
        }
    }

    /**
     * 字段超长,导致插入不成功
     */
    private void checkFields() {
        if (this.period.length() > 15) {
            this.period = this.period.substring(0, 10);
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFolder() {
        return folder;
    }

    public void setFolder(String folder) {
        this.folder = folder;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public LocalDateTime getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(LocalDateTime updateAt) {
        this.updateAt = updateAt;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }
}

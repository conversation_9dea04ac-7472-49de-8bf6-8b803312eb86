package shanks.scgl.bean.api.user;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

public class BindModel {
    @Expose
    private String phone;
    @Expose
    private String email;
    @Expose
    private String code;
    @Expose
    private String pushId;


    /**
     * 校验
     */
    public static boolean check(BindModel model) {
        return model != null
                && (!Strings.isNullOrEmpty(model.phone) || !Strings.isNullOrEmpty(model.email))
                && !Strings.isNullOrEmpty(model.code);
    }

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}

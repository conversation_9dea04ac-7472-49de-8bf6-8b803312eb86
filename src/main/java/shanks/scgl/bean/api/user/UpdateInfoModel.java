package shanks.scgl.bean.api.user;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;
import shanks.scgl.bean.db.User;
import shanks.scgl.bean.db.UserInfo;

/**
 * 用户更新信息，完善信息的Model
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class UpdateInfoModel {
    @Expose
    private String name;
    @Expose
    private String portrait;
    @Expose
    private String intro;
    @Expose
    private String sex;
    @Expose
    private String birthday;
    @Expose
    private String location;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPortrait() {
        return portrait;
    }

    public void setPortrait(String portrait) {
        this.portrait = portrait;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 把当前的信息，填充到用户Model中
     * 方便UserModel进行写入
     *
     * @param user User Model
     * @return User Model
     */
    public User updateToUser(User user) {
        UserInfo userInfo=user.loadInfo();
        if (!Strings.isNullOrEmpty(name))
            userInfo.setUserName(name);

        if (!Strings.isNullOrEmpty(portrait))
            userInfo.setFace(portrait);

        if (!Strings.isNullOrEmpty(intro))
            userInfo.setIntro(intro);

        if (!Strings.isNullOrEmpty(sex))
            userInfo.setSex(sex);

        if (!Strings.isNullOrEmpty(birthday))
            userInfo.setBirthday(birthday);

        if (!Strings.isNullOrEmpty(location))
            userInfo.setLocation(location);

        return user;
    }

    public static boolean check(UpdateInfoModel model) {
        // Model 不允许为null，
        // 并且只需要具有一个及其以上的参数即可
        return model != null
                && (!Strings.isNullOrEmpty(model.name) ||
                !Strings.isNullOrEmpty(model.portrait) ||
                !Strings.isNullOrEmpty(model.intro) ||
                !Strings.isNullOrEmpty(model.birthday) ||
                !Strings.isNullOrEmpty(model.location) ||
                !Strings.isNullOrEmpty(model.sex));
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}

package shanks.scgl.bean.api.user;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

public class Bind3rdModel {
    public static final int TYPE_QQ = 0;
    public static final int TYPE_SINA = 1;

    @Expose
    private String openId;
    @Expose
    private int type;

    /**
     * 校验
     */
    public static boolean check(Bind3rdModel model) {
        return model != null
                && !Strings.isNullOrEmpty(model.openId)
                && (model.type == TYPE_QQ || model.type == TYPE_SINA);
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}

package shanks.scgl.bean.api.social;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

public class FeedbackModel {
    @Expose
    private int type;
    @Expose
    private String content;
    @Expose
    private String ref;

    public static boolean check(FeedbackModel model) {
        return !(model == null || Strings.isNullOrEmpty(model.content)
                || model.type < 0 || model.type > 10);
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRef() {
        return ref;
    }

    public void setRef(String ref) {
        this.ref = ref;
    }
}

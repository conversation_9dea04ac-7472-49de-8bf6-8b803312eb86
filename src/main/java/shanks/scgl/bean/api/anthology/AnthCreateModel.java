package shanks.scgl.bean.api.anthology;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

/**
 * API请求的Model格式
 */
public class AnthCreateModel {
    @Expose
    private String id;
    @Expose
    private String name;
    @Expose
    private String cover;
    @Expose
    private String intro;
    @Expose
    private int type;

    public static boolean check(AnthCreateModel model) {
        return !Strings.isNullOrEmpty(model.getName());
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}

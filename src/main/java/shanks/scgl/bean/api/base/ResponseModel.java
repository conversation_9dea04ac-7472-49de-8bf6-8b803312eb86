package shanks.scgl.bean.api.base;

import com.google.gson.annotations.Expose;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Email:<EMAIL>
 * @version 1.0.0
 */
public class ResponseModel<M> implements Serializable {
    // 成功
    public static final int SUCCEED = 1;
    // 未知错误
    public static final int ERROR_UNKNOWN = 0;

    // 没有找到用户信息
    public static final int ERROR_NOT_FOUND_USER = 4041;
    // 没有找到群信息
    public static final int ERROR_NOT_FOUND_GROUP = 4042;
    // 没有找到群成员信息
    public static final int ERROR_NOT_FOUND_GROUP_MEMBER = 4043;
    //没有找到微博信息
    public static final int ERROR_NOT_FOUND_WEIBO = 4044;
    //没有找到诗集
    public static final int ERROR_NOT_FOUND_ANTH = 4045;
    //没有找到诗词
    public static final int ERROR_NOT_FOUND_POEM = 4046;
    //没有找到收藏记录
    public static final int ERROR_NOT_FOUND_KEEP = 4047;

    // 创建用户失败
    public static final int ERROR_CREATE_USER = 3001;
    // 创建群失败
    public static final int ERROR_CREATE_GROUP = 3002;
    // 创建群成员失败
    public static final int ERROR_CREATE_MESSAGE = 3003;
    //创建Weibo失败
    public static final int ERROR_CREATE_WEIBO=3004;
    //创建评论失败
    public static final int ERROR_CREATE_COMMENT=3005;

    // 请求参数错误
    public static final int ERROR_PARAMETERS = 4001;
    // 请求参数错误-已存在账户
    public static final int ERROR_PARAMETERS_EXIST_ACCOUNT = 4002;
    // 请求参数错误-已存在昵称
    public static final int ERROR_PARAMETERS_EXIST_NAME = 4003;
    // 请求参数错误-已存在手机号
    public static final int ERROR_PARAMETERS_EXIST_PHONE = 4004;
    // 请求参数错误-已存在邮箱
    public static final int ERROR_PARAMETERS_EXIST_EMAIL = 4005;
    //验证码错误
    public static final int ERROR_PARAMETERS_IDENTIFY_CODE=4006;
    //QQ号已经被绑定
    public static final int ERROR_PARAMETERS_EXIST_QQ=4007;
    //新浪微博已经被绑定
    public static final int ERROR_PARAMETERS_EXIST_SINA=4008;

    // 服务器错误
    public static final int ERROR_SERVICE = 5001;
    //请求过于频繁
    public static final int ERROR_SERVICE_BUSY = 5002;
    //OSS授权失败
    public static final int ERROR_OSS_PERM_GRANT = 5003;

    // 账户Token错误，需要重新登录
    public static final int ERROR_ACCOUNT_TOKEN = 2001;
    // 账户登录失败
    public static final int ERROR_ACCOUNT_LOGIN = 2002;
    public static final int ERROR_ACCOUNT_LOCKED = 2008;
    // 账户注册失败
    public static final int ERROR_ACCOUNT_REGISTER = 2003;
    // QQ号还没有绑定
    public static final int ERROR_ACCOUNT_QQ_NOT_BIND = 2004;
    //新浪微博还没有绑定
    public static final int ERROR_ACCOUNT_SINA_NOT_BIND = 2005;
    //手机号没有绑定
    public static final int ERROR_ACCOUNT_PHONE_NOT_BIND = 2006;
    //邮箱没有绑定
    public static final int ERROR_ACCOUNT_EMAIL_NOT_BIND = 2007;

    // 没有权限操作
    public static final int ERROR_ACCOUNT_NO_PERMISSION = 2010;

    //关注的人太多了
    public static final int ERROR_BUSINESS_TOO_MANY_FOLLOW =6001;
    //积分不足
    public static final int ERROR_BUSINESS_NO_SUFFICIENT_POINT =6002;
    //同一版本只能评分一次
    public static final int ERROR_BUSINESS_ALREADY_MARK =6003;
    //文件为空不能删除
    public static final int ERROR_BUSINESS_ANTH_NOT_EMPTY =6004;
    //限制文集大小
    public static final int ERROR_BUSINESS_ANTH_IS_FULL =6005;
    //作品已经被绑定
    public static final int ERROR_BUSINESS_POEM_IS_BIND =6006;
    //VIP
    public static final int ERROR_BUSINESS_NOT_VIP =6007;
    //被加入黑名单
    public static final int ERROR_BUSINESS_IS_BLACK =6008;

    public static final int ERROR=400;
    public static final int SUCCESS=200;

    @Expose
    private int code;
    @Expose
    private String message;
    @Expose
    private LocalDateTime time = LocalDateTime.now();
    @Expose
    private M result;

    public ResponseModel() {
        code = 1;
        message = "ok";
    }

    public ResponseModel(M result) {
        this();
        this.result = result;
    }

    public ResponseModel(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public ResponseModel(int code, String message, M result) {
        this.code = code;
        this.message = message;
        this.result = result;
    }

    public int getCode() {
        return code;
    }

    public boolean isSucceed() {
        return code == SUCCEED;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTime() {
        return time;
    }

    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    public M getResult() {
        return result;
    }

    public void setResult(M result) {
        this.result = result;
    }

    /**
     * @deprecated 用buildSuccess方法替代
     */
    public static <M> ResponseModel<M> buildOk() {
        return new ResponseModel<M>();
    }

    /**
     * @deprecated 用buildSuccess方法替代
     */
    public static <M> ResponseModel<M> buildOk(M result) {
        return new ResponseModel<M>(result);
    }

    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildParameterError() {
        return new ResponseModel<M>(ERROR_PARAMETERS, "Parameters Error.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildHavePhoneError() {
        return new ResponseModel<M>(ERROR_PARAMETERS_EXIST_PHONE, "Already have this phone.");
    }
    /**
     * @deprecated 用buildError方法替代
     */     
    public static <M> ResponseModel<M> buildHaveQQError() {
        return new ResponseModel<M>(ERROR_PARAMETERS_EXIST_QQ, "Already have this QQ.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildHaveSinaError() {
        return new ResponseModel<M>(ERROR_PARAMETERS_EXIST_SINA, "Already have this sina weibo.");
    }

    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildHaveEmailError() {
        return new ResponseModel<M>(ERROR_PARAMETERS_EXIST_EMAIL, "Already have this email.");
    }

    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildHaveNameError() {
        return new ResponseModel<M>(ERROR_PARAMETERS_EXIST_NAME, "Already have this user name.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildIdentifyCodeError() {
        return new ResponseModel<M>(ERROR_PARAMETERS_IDENTIFY_CODE, "Identify code is error.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildHaveAccountError() {
        return new ResponseModel<M>(ERROR_PARAMETERS_EXIST_ACCOUNT, "Already have this account name.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildServiceError() {
        return new ResponseModel<M>(ERROR_SERVICE, "Service Error.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildServiceBusyError() {
        return new ResponseModel<M>(ERROR_SERVICE_BUSY, "Service is busy.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildOssPermGrantError() {
        return new ResponseModel<M>(ERROR_OSS_PERM_GRANT, "Grant oss permission failed.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildNotFoundUserError(String str) {
        return new ResponseModel<M>(ERROR_NOT_FOUND_USER, str != null ? str : "Not Found User.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildNotFoundGroupError(String str) {
        return new ResponseModel<M>(ERROR_NOT_FOUND_GROUP, str != null ? str : "Not Found Group.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildNotFoundGroupMemberError(String str) {
        return new ResponseModel<M>(ERROR_NOT_FOUND_GROUP_MEMBER, str != null ? str : "Not Found GroupMember.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildNotFoundWeiboError(String str) {
        return new ResponseModel<M>(ERROR_NOT_FOUND_WEIBO, str != null ? str : "Not Found weibo.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildAccountError() {
        return new ResponseModel<M>(ERROR_ACCOUNT_TOKEN, "Account Error; you need login.");
    }

    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildLoginError() {
        return new ResponseModel<M>(ERROR_ACCOUNT_LOGIN, "Account or password error.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildLockedError() {
        return new ResponseModel<M>(ERROR_ACCOUNT_LOCKED, "Account has been locked.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildQqNotBindError() {
        return new ResponseModel<M>(ERROR_ACCOUNT_QQ_NOT_BIND, "QQ is not bind yet.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildSinaNotBindError() {
        return new ResponseModel<M>(ERROR_ACCOUNT_SINA_NOT_BIND, "Sina weibo is not bind yet.");
    }

    public static <M> ResponseModel<M> buildPhoneNotBindError() {
        return new ResponseModel<M>(ERROR_ACCOUNT_PHONE_NOT_BIND, "Phone number is not bind yet.");
    }

    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildEmailNotBindError() {
        return new ResponseModel<M>(ERROR_ACCOUNT_EMAIL_NOT_BIND, "Email is not bind yet.");
    }

    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildRegisterError() {
        return new ResponseModel<M>(ERROR_ACCOUNT_REGISTER, "Register failed.");
    }

    /**
     * @deprecated 用buildError方法替代
     */ 
    public static <M> ResponseModel<M> buildNoPermissionError() {
        return new ResponseModel<M>(ERROR_ACCOUNT_NO_PERMISSION, "You do not have permission to operate.");
    }

    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildCreateError(int type) {
        return new ResponseModel<M>(type, "Create failed.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildNotFoundError(int type) {
        return new ResponseModel<M>(type, "Not found.");
    }

    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildTooManyFollowError() {
        return new ResponseModel<M>(ERROR_BUSINESS_TOO_MANY_FOLLOW, "Too many follow.");
    }
    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildNoSufficientPointError() {
        return new ResponseModel<M>(ERROR_BUSINESS_NO_SUFFICIENT_POINT, "No sufficient point.");
    }

    /**
     * @deprecated 用buildError方法替代
     */
     public static <M> ResponseModel<M> buildAlreadyMarkError() {
        return new ResponseModel<M>(ERROR_BUSINESS_ALREADY_MARK, "You have already marked.");
    }

    /**
     * @deprecated 用buildError方法替代
     */
    public static <M> ResponseModel<M> buildBusinessError(int type) {
        return new ResponseModel<M>(type, "Business error.");
    }

    /**
     * 构建错误响应, 返回错误信息
     */
    public static <M> ResponseModel<M> buildError(String message) {
        return new ResponseModel<M>(ERROR, message);
    }

    /**
     * 构建成功响应, 返回成功信息
     */
    public static <M> ResponseModel<M> buildSuccess(String message) {
        return new ResponseModel<M>(SUCCESS, message);
    }

    /**
     * 构建成功响应, 返回成功信息和结果
     */
    public static <M> ResponseModel<M> buildSuccess(String message, M result) {
        return new ResponseModel<M>(SUCCESS, message, result);
    }

}
package shanks.scgl.bean.api.rule;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

/**
 * API请求的Model格式
 */
public class RuleCreateModel {
    @Expose
    private String id;
    @Expose
    private String name;
    @Expose
    private String category;
    @Expose
    private String type;
    @Expose
    private String alias;
    @Expose
    private String recommend;
    @Expose
    private String help;
    @Expose
    private String log;
    @Expose
    private String clientHash;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public String getHelp() {
        return help;
    }

    public void setHelp(String help) {
        this.help = help;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public static boolean check(RuleCreateModel model) {
        return !Strings.isNullOrEmpty(model.getId());
    }

    public String getClientHash() {
        return clientHash;
    }

    public void setClientHash(String clientHash) {
        this.clientHash = clientHash;
    }
}

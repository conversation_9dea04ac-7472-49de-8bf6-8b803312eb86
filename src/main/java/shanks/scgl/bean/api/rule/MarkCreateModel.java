package shanks.scgl.bean.api.rule;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

/**
 * API请求的Model格式
 */
public class MarkCreateModel {
    @Expose
    private float mark;
    @Expose
    private String content;
    @Expose
    private String version;
    @Expose
    private String rid;

    public float getMark() {
        return mark;
    }

    public void setMark(float mark) {
        this.mark = mark;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public static boolean check(MarkCreateModel model) {
        return !Strings.isNullOrEmpty(model.getRid());
    }
}

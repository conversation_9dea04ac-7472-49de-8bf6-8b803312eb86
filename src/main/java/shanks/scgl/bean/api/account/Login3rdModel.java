package shanks.scgl.bean.api.account;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

public class Login3rdModel {
    public static final int TYPE_QQ = 0;
    public static final int TYPE_SINA = 1;

    @Expose
    private String openId;
    @Expose
    private int type;
    @Expose
    private String pushId;

    /**
     * 校验
     */
    public static boolean check(Login3rdModel model) {
        return model != null
                && !Strings.isNullOrEmpty(model.openId)
                && (model.type == TYPE_QQ || model.type == TYPE_SINA);
    }

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}

package shanks.scgl.bean.api.account;

import com.google.gson.annotations.Expose;
import org.apache.http.util.TextUtils;

public class LaunchModel {
    @Expose
    private String id;
    @Expose
    private String osVersion;
    @Expose
    private String osModel;
    @Expose
    private String appVersion;
    @Expose
    private String token;

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getOsModel() {
        return osModel;
    }

    public void setOsModel(String osModel) {
        this.osModel = osModel;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public static boolean check(LaunchModel model) {
        return model != null && !TextUtils.isEmpty(model.getId());
    }
}

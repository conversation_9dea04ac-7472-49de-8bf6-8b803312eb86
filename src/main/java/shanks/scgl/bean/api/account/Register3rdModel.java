package shanks.scgl.bean.api.account;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

/**
 * 注册使用的请求Model
 *
 * @version 2017/9/22 Created by book
 */

public class Register3rdModel {
    public static final int TYPE_QQ = 0;
    public static final int TYPE_SINA = 1;

    @Expose
    private String openId;
    @Expose
    private int type;
    @Expose
    private String password;
    @Expose
    private String name;
    @Expose
    private String pushId;

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public static boolean check(Register3rdModel model) {
        return model != null
                && !Strings.isNullOrEmpty(model.openId)
                && !Strings.isNullOrEmpty(model.name)
                && !Strings.isNullOrEmpty(model.password)
                && (model.type == TYPE_QQ || model.type == TYPE_SINA);
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}

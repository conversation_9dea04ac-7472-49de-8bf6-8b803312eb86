package shanks.scgl.bean.api.account;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

/**
 * 默认只支持手机号注册
 */

@SuppressWarnings("unused")
public class RegisterModel {
    @Expose
    private String phone;
    @Expose
    private String password;
    @Expose
    private String name;
    @Expose
    private String code;
    @Expose
    private String pushId;

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }


    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    /**
     * 密码不能为空&&昵称不能为空&&邮箱不为空或者手机号不为空
     */
    public static boolean check(RegisterModel model) {
        return model != null
                && !Strings.isNullOrEmpty(model.password)
                && !Strings.isNullOrEmpty(model.name)
                && !Strings.isNullOrEmpty(model.phone)
                && !Strings.isNullOrEmpty(model.code);
    }

    public static boolean checkForVerify(RegisterModel model) {
        return model != null
                && !Strings.isNullOrEmpty(model.password)
                && !Strings.isNullOrEmpty(model.name)
                && !Strings.isNullOrEmpty(model.phone);
    }


}

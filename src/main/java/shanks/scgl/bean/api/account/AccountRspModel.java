package shanks.scgl.bean.api.account;

import com.google.gson.annotations.Expose;
import shanks.scgl.bean.card.UserCard;
import shanks.scgl.bean.db.User;

import java.time.LocalDateTime;

public class AccountRspModel {
    //用户基本信息.客户端没有解析UserCard,而是直接解析成User,所以名称用了user
    @Expose
    private UserCard user;
    //当前登录的账号
    @Expose
    private String account;
    //登录成功后获取的token,可以通过token获取用户的所有信息
    @Expose
    private String token;
    //标识是否已经绑定到了设备PushID
    @Expose
    private boolean isBind;
    @Expose
    private String phone;
    @Expose
    private String email;
    @Expose
    private String sinaId;
    @Expose
    private String qqId;
    @Expose
    private LocalDateTime createAt;

    public AccountRspModel(User user) {
        //默认无绑定
        this(user, false);
    }

    public AccountRspModel(User user, boolean isBind) {
        this.user = new UserCard(user);
        this.account = user.getAccount();
        this.token = user.getToken();
        this.isBind = isBind;
        this.phone=user.getPhone();
        this.email=user.getEmail();
        this.qqId=user.getQqId();
        this.sinaId=user.getSinaId();
        this.createAt =user.getCreateAt();
    }

    public UserCard getUser() {
        return user;
    }

    public void setUser(UserCard user) {
        this.user = user;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public boolean isBind() {
        return isBind;
    }

    public void setBind(boolean bind) {
        isBind = bind;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSinaId() {
        return sinaId;
    }

    public void setSinaId(String sinaId) {
        this.sinaId = sinaId;
    }

    public String getQqId() {
        return qqId;
    }

    public void setQqId(String qqId) {
        this.qqId = qqId;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }
}

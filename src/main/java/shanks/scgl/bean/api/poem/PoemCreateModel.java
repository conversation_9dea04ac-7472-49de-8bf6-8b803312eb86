package shanks.scgl.bean.api.poem;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

import java.time.LocalDateTime;

/**
 * API请求的Model格式
 */
public class PoemCreateModel {
    //id来源于客户端
    @Expose
    private String id;
    @Expose
    private String title;
    @Expose
    private String period;
    @Expose
    private String author;
    @Expose
    private String content;
    @Expose
    private String folder;
    @Expose
    private String ruleId;
    @Expose
    private String clientHash;
    //创建时间来源于客户端
    @Expose
    private LocalDateTime createAt = LocalDateTime.now();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFolder() {
        return folder;
    }

    public void setFolder(String folder) {
        this.folder = folder;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public static boolean check(PoemCreateModel model) {
        return !Strings.isNullOrEmpty(model.id);
    }

    public String getClientHash() {
        return clientHash;
    }

    public void setClientHash(String clientHash) {
        this.clientHash = clientHash;
    }
}

package shanks.scgl.bean.api.weibo;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

public class WeiboCreateModel {
    //微博的id,更新的时候用
    @Expose
    private int id;
    @Expose
    private int type;
    @Expose
    private String content;
    @Expose
    private String poemId;
    @Expose
    private String ruleId;
    @Expose
    private String anthId;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPoemId() {
        return poemId;
    }

    public void setPoemId(String poemId) {
        this.poemId = poemId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public static boolean check(WeiboCreateModel model) {
        return !(Strings.isNullOrEmpty(model.content) || Strings.isNullOrEmpty(model.anthId)
                || model.type < 0 || model.type > 10 || model.content.length() < 8);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAnthId() {
        return anthId;
    }

    public void setAnthId(String anthId) {
        this.anthId = anthId;
    }
}

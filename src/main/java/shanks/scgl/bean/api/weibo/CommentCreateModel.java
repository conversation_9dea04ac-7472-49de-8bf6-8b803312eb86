package shanks.scgl.bean.api.weibo;

import com.google.common.base.Strings;
import com.google.gson.annotations.Expose;

public class CommentCreateModel {
    @Expose
    private int wid;
    @Expose
    private int parent;
    @Expose
    private String content;

    public static boolean check(CommentCreateModel model) {
        return !Strings.isNullOrEmpty(model.content);
    }

    public int getWid() {
        return wid;
    }

    public void setWid(int wid) {
        this.wid = wid;
    }

    public int getParent() {
        return parent;
    }

    public void setParent(int parent) {
        this.parent = parent;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}

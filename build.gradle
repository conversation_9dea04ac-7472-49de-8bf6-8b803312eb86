group 'scgl'
version '1.0-SNAPSHOT'

apply plugin: 'java'
apply plugin: 'war'

sourceCompatibility = 1.8

repositories {
    maven { url 'https://maven.aliyun.com/repository/public' }
    maven { url 'https://maven.aliyun.com/repository/google' }
    mavenCentral()
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'com.google.code.gson:gson:2.8.0'
    implementation 'com.google.guava:guava:21.0'

    // Jersey 轻量级Restful接口框架
    implementation 'org.glassfish.jersey.core:jersey-client:2.26-b03'
    implementation 'org.glassfish.jersey.core:jersey-server:2.26-b03'
    implementation 'org.glassfish.jersey.containers:jersey-container-servlet:2.26-b03'
    // 也是一个 Json 解析库
    implementation 'org.glassfish.jersey.media:jersey-media-json-jackson:2.26-b03'

    // 数据库操作框架
    // https://mvnrepository.com/artifact/org.hibernate/hibernate-core
    implementation 'org.hibernate:hibernate-core:5.2.9.Final'
    // https://mvnrepository.com/artifact/org.hibernate/hibernate-entitymanager
    implementation 'org.hibernate:hibernate-entitymanager:5.2.9.Final'
    // https://mvnrepository.com/artifact/org.hibernate/hibernate-c3p0
    implementation 'org.hibernate:hibernate-c3p0:5.2.9.Final'

    // MySQL 驱动库
    // https://mvnrepository.com/artifact/mysql/mysql-connector-java
    implementation group: 'mysql', name: 'mysql-connector-java', version: '8.0.16'
    //compile group: 'mysql', name: 'mysql-connector-java', version: '6.0.6'

    //OSS
    implementation 'com.aliyun:aliyun-java-sdk-sts:3.0.0'
    implementation 'com.aliyun:aliyun-java-sdk-core:3.5.0'

    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.8.0'

    implementation 'javax.xml.bind:jaxb-api:2.3.1'
    implementation 'com.sun.xml.bind:jaxb-impl:2.3.1'
    implementation 'org.glassfish.jaxb:jaxb-runtime:2.3.1'
    implementation 'javax.activation:activation:1.1.1'
}
